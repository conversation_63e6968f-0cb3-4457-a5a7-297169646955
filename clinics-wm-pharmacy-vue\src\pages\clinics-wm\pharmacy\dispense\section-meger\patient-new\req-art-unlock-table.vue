<script setup lang="ts">
import { ref, reactive, watchEffect } from 'vue'
import { Modal, message } from "ant-design-vue";
import type { PaginationProps } from "ant-design-vue";
import {
  sectionMegerArtDetailPageApi,
  sectionMegerArtListApi,
  sectionMegerPrintHeaderApi,
  stockReserveBySectionApi,
  finishedReqBySectionApi
} from "~/api/clinics_wm/wmreq.ts";
import { getPrintTemplateFullApi, PrintTemplateType } from "~/api/hip/printtemplate.ts";
import lodop from "~/components/lodop";
import { createVNode } from "vue";
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";

interface Props {
  deptCode?: string;
  sectionId?: number;
  visitId?: number;
}

const props = defineProps<Props>()
const showShortFlag = ref(true)
const btnLoading = ref(false)

// 使用与 req-art-locked-table.vue 相同的 DetailRecord 接口
interface DetailRecord {
  wmReqid: string;
  lineNo: number;
  artId: string;
  artName: string;
  artSpec: string;
  producer: string;
  bedNo: string;
  patientName: string;
  shortCells: number;
  cellsDelivered: number;
  cellsReserved: number;
  cellUnit: string;
  packUnit: string;
  miCode: string;
  amount: number;
}

interface TableModel {
  loading: boolean;
  columns: Array<{
    title: string;
    dataIndex: string;
    width?: number;
    align?: 'left' | 'right' | 'center';
  }>;
  dataSource: DetailRecord[];
  pagination: PaginationProps;
  customRow: (record: DetailRecord) => { style: { color: string } };
  loadDataSource: () => Promise<void>;
}

const detailTableModel = reactive<TableModel>({
  loading: false,
  columns: [{
    title: '序号',
    dataIndex: 'index',
    width: 60,
    align: 'right'
  }, {
    title: '申请单',
    dataIndex: 'wmReqid',
    width: 80,
    align: 'right'
  }, {
    title: '品种ID',
    dataIndex: 'artId',
    width: 80,
    align: 'right'
  }, {
    title: '品名|规格|厂家',
    dataIndex: 'name',
    align: 'left'
  }, {
    title: '申领数量',
    dataIndex: 'shortCells',
    width: 75,
    align: 'right'
  }, {
    title: '已发药数',
    dataIndex: 'cellsDelivered',
    width: 75,
    align: 'right'
  }, {
    title: '销售金额',
    dataIndex: 'amount',
    width: 75,
    align: 'right'
  }, {
    title: '医保编码',
    dataIndex: 'miCode',
    width: 150,
    align: 'left'
  }],
  dataSource: [],
  loadDataSource: async () => {
    if (props.deptCode && props.visitId) {
      detailTableModel.loading = true
      try {
        const {data} = await sectionMegerArtDetailPageApi({
          ...searchFormModel,
          deptCode: props.deptCode,
          sectionId: props.sectionId,
          visitId: props.visitId,
          lockedFlag: 1,
          showShortFlag: showShortFlag.value ? 1 : 0,
          pageNum: detailTableModel.pagination.current,
          pageSize: detailTableModel.pagination.pageSize,
        })
        detailTableModel.dataSource = data.list
        detailTableModel.pagination.total = data.total ?? 0

        // 发射数据加载完成事件
        emit('dataLoaded')
      } catch (err) {
        console.error('加载数据失败:', err)
      } finally {
        detailTableModel.loading = false
      }
    } else {
      detailTableModel.dataSource = []

      // 发射数据加载完成事件
      emit('dataLoaded')
    }
  },
  pagination: reactive<PaginationProps>({
    pageSize: 100,
    current: 1,
    total: 0,
    showQuickJumper: true,
    showSizeChanger: true,
    showTotal: (total: number) => `共：${total} 条`,
    onChange(current: number, pageSize: number) {
      detailTableModel.pagination.pageSize = pageSize
      detailTableModel.pagination.current = current
      detailTableModel.loadDataSource()
    },
  }),
  customRow: (record: DetailRecord) => {
    return {
      style: {
        color: record.shortCells > record.cellsDelivered + record.cellsReserved ? 'red' : '',
      }
    }
  }
})

const searchFormModel = reactive<{
  S_EQ_t_wm_req_detail__Art_ID?: string;
  S_LIKE_t_article__Art_Name?: string;
  S_LIKE_t_visit__Patient_Name?: string;
}>({})

watchEffect(async () => {
  await detailTableModel.loadDataSource()
})

const reload = async () => {
  detailTableModel.pagination.current = 1
  await detailTableModel.loadDataSource()
}

const clearData = () => {
  detailTableModel.dataSource = []
  detailTableModel.pagination.total = 0
  detailTableModel.pagination.current = 1

  // 发射数据加载完成事件
  emit('dataLoaded')
}

const emit = defineEmits(['ok', 'refresh', 'locked', 'dataLoaded'])

// 库存锁定
const onReserve = async () => {
  btnLoading.value = true
  try {
    await stockReserveBySectionApi({
      deptCode: props.deptCode,
      sectionId: props.sectionId,
      visitId: props.visitId,
    })
    message.success('操作成功')
    // await reload()
    // 通知父组件刷新其他表格
    // emit('ok')
    // 改成通知父组件已锁定，父组件切换待出库去发药
    emit('locked')
  } catch (err) {
    console.error('库存锁定失败:', err)
    message.error('库存锁定失败')
  } finally {
    btnLoading.value = false
  }
}

const onFinishedReq = async () => {
  Modal.confirm({
    title: '驳回申请?',
    icon: createVNode(ExclamationCircleOutlined),
    content: createVNode('div', {}, '此操作会将当前病区所有的领用申请全部驳回，请确定不需要再发药了再操作!'),
    onOk() {
      finishedReq()
    },
    onCancel() {
    },
    class: 'test',
  });
}

const finishedReq = async () => {
  btnLoading.value = true
  try {
    await finishedReqBySectionApi({
      deptCode: props.deptCode,
      sectionId: props.sectionId,
      visitId: props.visitId,
    })
    message.success('操作成功')
    // 通知父组件刷新左侧表格
    emit('refresh')
  } catch (err) {
    console.error('结束请求失败:', err)
    message.error('结束请求失败')
  } finally {
    btnLoading.value = false
  }
}

// 获取数据数量的方法
const getDataCount = () => {
  const count = detailTableModel.dataSource.length
  console.log('patient-new 未锁定明细 getDataCount 被调用:', count)
  return count
}

defineExpose({
  reload,
  clearData,
  getDataCount
})
</script>

<template>
  <base-table 
    :loading="detailTableModel.loading" 
    :columns="detailTableModel.columns" 
    :height="600"
    :dataSource="detailTableModel.dataSource" 
    :pagination="detailTableModel.pagination" 
    :custom-row="detailTableModel.customRow"
    :rowKey="(item: DetailRecord) => item.wmReqid + '-' + item.lineNo">
    <template #btns>
      <a-space size="middle" class="m-l-10px">
        <a-space>
          <text>条目ID:</text>
          <a-input v-model:value="searchFormModel.S_EQ_t_wm_req_detail__Art_ID" style="width: 80px" @pressEnter="reload"/>
        </a-space>
        <a-space>
          <text>品名:</text>
          <a-input v-model:value="searchFormModel.S_LIKE_t_article__Art_Name" style="width: 150px" @pressEnter="reload"/>
        </a-space>
        <a-space>
          <text>患者:</text>
          <a-input v-model:value="searchFormModel.S_LIKE_t_visit__Patient_Name" style="width: 100px" @pressEnter="reload"/>
        </a-space>
        <a-checkbox v-model:checked="showShortFlag" @change="reload">只显示未完成</a-checkbox>
        <a-button 
          :loading="btnLoading" 
          :disabled="detailTableModel.dataSource.length <= 0" 
          type="primary" 
          @click="onReserve()">库存锁定</a-button>
<!--        <a-button -->
<!--          :loading="btnLoading" -->
<!--          :disabled="detailTableModel.dataSource.length <= 0" -->
<!--          danger -->
<!--          @click="onFinishedReq()">驳回申请</a-button>-->
      </a-space>
    </template>
    <template #bodyCell="{ column, record, index }">
      <template v-if="column?.dataIndex === 'index'">
        {{ index + 1 }}
      </template>
      <template v-if="column?.dataIndex === 'name'">
        {{ record.artName }} {{ record.artSpec }}<br/>{{ record.producer }}
      </template>
      <template v-if="column?.dataIndex === 'shortCells'">
        <span v-if="record.shortCells">{{ record.shortCells }}{{ record.cellUnit }}</span>
      </template>
      <template v-if="column?.dataIndex === 'cellsDelivered'">
        <span v-if="record.cellsDelivered > 0">{{ record.cellsDelivered }}{{ record.cellUnit }}</span>
      </template>
    </template>
  </base-table>
</template>

<style lang="less" scoped>
.bg-fff {
  background-color: #fff;
  :deep(.ant-tabs-nav) {
    margin-bottom: 0;
  }
  :deep(.ant-tabs-nav-list) {
    margin-left: 10px;
  }
}
</style> 