<!--药房-库房业务-药房发药-门诊发药/住院发药-患者-处方药品详情-->
<script setup lang="ts">
import type {PaginationProps} from 'ant-design-vue'
import { recipeDetailPageByVisitApi } from '~/api/clinics_wm/wmbill.ts'
const showArtSubType = import.meta.env.VITE_APP_DISPENSE_SHOW_ART_SUB_TYPE === 1 || import.meta.env.VITE_APP_DISPENSE_SHOW_ART_SUB_TYPE === '1'

const message = useMessage()
const props = defineProps({
  deptCode: { type: String },
  visitId: { type: Number },
  finishedFlag: { type: Number },
  recipeId: { type: Number }
})

// 加载数据
interface TableModel {
  loading?: Boolean,
  columns: any[],
  dataSource: any[],
  selectedRowKeys: any[]
}

const artSubTypeColumns: TableColumnType[] = [
  {
    title: '条目亚类',
    dataIndex: 'artSubTypeName',
    align: 'center',
    width: 100,
    fixed: 'left'
  }
]

const columns = computed(() => {
  if (showArtSubType) {
    return [...artSubTypeColumns, ...billDetailTableModel.columns]
  } else {
    return billDetailTableModel.columns
  }
})

const billDetailTableModel = reactive<TableModel>({
  loading: false,
  columns: [{
    title: '品名|规格|厂家',
    dataIndex: 'name',
    width: 250,
    align: 'left',
    fixed: 'left',
  }, {
    title: '单价',
    dataIndex: 'packPrice',
    width: 90,
    align: 'right',
    fixed: 'left'
  }, {
    title: '发药数量',
    dataIndex: 'totalPacks',
    width: 90,
    align: 'right',
    fixed: 'left'
  }, {
    title: '金额',
    dataIndex: 'amount',
    width: 90,
    align: 'right',
    fixed: 'left'
  }, {
    title: '库存量',
    dataIndex: 'currentTotal',
    width: 120,
    align: 'right',
  }, {
    title: '批号',
    dataIndex: 'batchNo',
    width: 110,
    align: 'center'
  }, {
    title: '有效期',
    dataIndex: 'expiry',
    width: 90,
    align: 'center'
  }, {
    title: '货位编码',
    dataIndex: 'rackNo',
    width: 90,
    align: 'center'
  }, {
    title: '批准文号',
    dataIndex: 'approvalNo',
    width: 170,
    align: 'left'
  }, {
    title: '医保编码',
    dataIndex: 'miCode',
    width: 230,
    align: 'left'
  }, {
    title: '品种ID',
    dataIndex: 'artId',
    width: 80,
    align: 'right'
  }, {
    title: '处方号',
    dataIndex: 'recipeId',
    width: 90,
    align: 'right',
  }, {
    title: '行号',
    dataIndex: 'lineNo',
    width: 60,
    align: 'right'
  }],
  dataSource: [],
  sumAmount: null,
  loadDataSource: async () => {
    if (props.visitId && props.deptCode) {
      billDetailTableModel.loading = true
      try {
        let sortParams = {}
        if (showArtSubType) {
          sortParams = {
            sidx: 't_art_subtype.Display_Order, t_wm_bill_detail.Art_ID, t_wm_bill_detail.Stock_No',
            order: 'asc',
          }
        }
        const {data} = await recipeDetailPageByVisitApi({
          ...sortParams,
          finishedFlag: props.finishedFlag,
          S_EQ_t_wm_bill__Recipe_ID: props.recipeId,
          S_EQ_T_WM_REQ__DEPT_CODE: props.deptCode,
          visitId: props.visitId,
          S_EQ_t_wm_req__Req_Type: 3,
          pageNum: billDetailTableModel.pagination.current,
          pageSize: billDetailTableModel.pagination.pageSize,
        })
        billDetailTableModel.dataSource = data.list
        billDetailTableModel.pagination.total = data.total ?? 0
        billDetailTableModel.loading = false

        billDetailTableModel.sumAmount = data.extra.sumAmount
      } catch (err) {
        console.log(err)
        billDetailTableModel.loading = false
      }
    } else {
      billDetailTableModel.dataSource = []
      billDetailTableModel.sumAmount = null
    }
  },
  pagination: reactive<PaginationProps>({
    pageSize: 100,
    current: 1,
    total: 0,
    showQuickJumper: true,
    showTotal: total => `共：${billDetailTableModel.pagination.total} 条`,
    onChange(current, pageSize) {
      billDetailTableModel.pagination.pageSize = pageSize
      billDetailTableModel.pagination.current = current
      billDetailTableModel.loadDataSource()
    },
  }),
  customRow: (record) => {
    return {
      style: {
        color: record.relativeWbseqid ? 'red' : '',
      }
    }
  }
})

watchEffect(async () => {
  await billDetailTableModel.loadDataSource()
})

// 合集
const tableSummary = computed(() => {
    return [{
      index: 0,
      colSpan: 1,
      label: '合计：',
      style: {}
    }, {
      index: showArtSubType ? 4 : 3,
      colSpan: 1,
      label: billDetailTableModel.sumAmount,
      style: {
        textAlign: 'right'
      }
    }]
  })
</script>
<template>
  <base-table 
    :loading="billDetailTableModel.loading" 
    :columns="columns"
    :dataSource="billDetailTableModel.dataSource"
    :custom-row="billDetailTableModel.customRow"
    :pagination="billDetailTableModel.pagination"
    :rowKey="(record) => record.wbSeqid + '-' + record.lineNo"
    :scroll="{ y: 'calc(100vh - 380px)'}"
    :summary="tableSummary">
    <template #bodyCell="{ column, record, index }">
      <template v-if="column?.dataIndex === 'index'">
        {{ index + 1 }}
      </template>
      <template v-if="column?.dataIndex === 'name'">
        {{ record.artName }} {{ record.artSpec }}<br/>{{ record.producer }}
      </template>
      <template v-if="column?.dataIndex === 'packCells'">
        {{ record.packCells }} {{ record.cellUnit }} / {{ record.packUnit }}
      </template>
      <template v-if="column?.dataIndex === 'totalPacks'">
        <span v-if="record.totalPacks">{{ record.totalPacks }}{{ record.packUnit }}</span><span v-if="record.totalCells">{{ record.totalCells }}{{ record.cellUnit }}</span>
      </template>
      <template v-if="column?.dataIndex === 'packPrice'">
        <span v-if="record.totalPacks">
          {{ record.packPrice ? `${record.packPrice}/${record.packUnit}` : (record.cellPrice ? `${record.cellPrice}/${record.cellUnit}` : '') }}
        </span>
        <span v-else-if="record.totalCells">
          {{ record.cellPrice ? `${record.cellPrice}/${record.cellUnit}` : (record.packPrice ? `${record.packPrice}/${record.packUnit}` : '') }}
        </span>
      </template>
      <template v-if="column?.dataIndex === 'currentTotal'">
        <span v-if="record.deptTotalPacks">{{ record.deptTotalPacks }}{{ record.packUnit }}</span><span v-if="record.deptTotalCells">{{ record.deptTotalCells }}{{ record.cellUnit }}</span>
      </template>
    </template>
  </base-table>
</template>
<style lang="less" scoped>
.bg-fff {
  background-color: #fff;
  // padding: 8px 0 0 8px;

  :deep(.ant-tabs-nav) {
    margin-bottom: 0;
  }

  :deep(.ant-tabs-nav-list) {
    margin-left: 10px;
  }
}
</style>
