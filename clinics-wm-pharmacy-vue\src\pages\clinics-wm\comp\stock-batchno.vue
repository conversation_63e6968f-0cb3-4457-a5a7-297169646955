<!--药房-效期预警-->
<script setup lang="ts">
import type { PaginationProps } from 'ant-design-vue'
import { pageApi, downloadExcelApi } from '~/api/clinics_wm/deptstock.ts'
import { listApi as findAllDeptLsApi } from "~/api/clinics_wm/deptcustmap";
import help from "~/utils/help";
// import { findStockEnabledAllApi } from "~/api/hip/cattype.ts";
import BatchExpiryUpdate from './batch-expiry-update.vue'
import { StockReqCat } from '@mh-hip/art-cat'
import { ArtSubTypeDict } from '@mh-hip/art-sub-type'
import { WholeBox } from '@mh-wm/whole-box'

const props = defineProps(['expiryDays', 'modal'])

// 加载数据
interface TableModel {
  loading?: Boolean,
  columns: any[],
  dataSource: any[],
  selectedRowKeys: any[],
  pagination: any
}
const tableModel = reactive<TableModel>({
  loading: false,
  columns: [{
    title: '序号',
    dataIndex: 'index',
    width: 60,
    align: 'center',
    fixed: 'left'
  }, {
    title: '品种ID',
    dataIndex: 'artId',
    width: 80,
    align: 'center',
    fixed: 'left'
  }, {
    title: '品名',
    dataIndex: 'artName',
    width: 180,
    ellipsis: true,
    align: 'left',
    fixed: 'left'
  }, {
    title: '规格',
    dataIndex: 'artSpec',
    width: 150,
    ellipsis: true,
    align: 'left'
  }, {
    title: '厂家',
    dataIndex: 'producer',
    width: 250,
    ellipsis: true,
    align: 'left'
  }, {
    title: '整包单位',
    dataIndex: 'packUnit',
    width: 120,
    align: 'center'
  }, {
    title: '批号',
    dataIndex: 'batchNo',
    width: 120,
    align: 'right'
  }, {
    title: '整包成本单价',
    dataIndex: 'packPrice',
    width: 120,
    align: 'right'
  }, {
    title: '拆零成本单价',
    dataIndex: 'cellPrice',
    width: 120,
    align: 'right'
  }, {
    title: '加成比例',
    dataIndex: 'pctAdd',
    width: 120,
    align: 'right'
  }, {
    title: '整包单销售价',
    dataIndex: 'salePackPrice',
    width: 120,
    align: 'right'
  }, {
    title: '拆零销售单价',
    dataIndex: 'saleCellPrice',
    width: 120,
    align: 'right'
  }, {
    title: '生产日期',
    dataIndex: 'dateManufactured',
    width: 120,
    align: 'right'
  }, {
    title: '有效期至',
    dataIndex: 'expiry',
    width: 120,
    align: 'right'
  }, {
    title: '临期天数',
    dataIndex: 'expiryDays',
    width: 120,
    align: 'right'
  }, {
    title: '批准文号',
    dataIndex: 'approvalNo',
    width: 180,
    align: 'left'
  }, {
    title: '允许拆零',
    dataIndex: 'splittable',
    width: 80,
    align: 'center',
    customRender: ({ text }: { text: number }) => {
      return text ? text === 1 ? '是' : '' : ''
    },
  }, {
    title: '医保编码',
    dataIndex: 'miCode',
    width: 240,
    align: 'left'
  }, {
    title: '包装材质',
    dataIndex: 'packMaterial',
    width: 150,
    ellipsis: true,
    align: 'left'
  }, {
    title: '剂型名称',
    dataIndex: 'dosageForm',
    width: 120,
    align: 'left'
    // }, {
    //   title: '包装单位',
    //   dataIndex: 'packUnit',
    //   width: 120,
    //   align: 'left'
    // }, {
    //   title: '制剂单位',
    //   dataIndex: 'cellUnit',
    //   width: 120,
    //   align: 'left'
  }, {
    title: '货位编码',
    dataIndex: 'rackNo',
    width: 90,
    align: 'center'
  }, {
    title: '批次',
    dataIndex: 'stockNo',
    width: 120,
    align: 'right'
  }, {
    title: '成本总额',
    dataIndex: 'costAmount',
    width: 120,
    align: 'right',
    fixed: 'right'
  }, {
    title: '销售总额',
    dataIndex: 'saleAmount',
    width: 120,
    align: 'right',
    fixed: 'right'
  }, {
    title: '仓库总库存',
    dataIndex: 'deptTotalPacks',
    width: 120,
    align: 'right',
    fixed: 'right'
  }, {
    title: '批次库存',
    dataIndex: 'currentTotal',
    width: 120,
    align: 'right',
    fixed: 'right'
  }, {
    title: '操作',
    dataIndex: 'action',
    width: 200,
    align: 'center',
    fixed: 'right'
  }],
  dataSource: [],
  loadDataSource: async () => {
    tableModel.loading = true
    try {
      const { data } = await pageApi({
        ...searchFormModel,
        sidx: 't_article.Art_ID, t_dept_stock.stock_no',
        order: 'asc',
        pageNum: tableModel.pagination.current,
        pageSize: tableModel.pagination.pageSize,
      })
      tableModel.dataSource = data.list
      tableModel.pagination.total = data.total ?? 0
      tableModel.sumRow = data.extra
      tableModel.loading = false
    } catch (err) {
      console.log(err)
      tableModel.loading = false
    }
  },
  selectedRowKeys: [],
  pagination: reactive<PaginationProps>({
    pageSize: 10,
    current: 1,
    total: 0,
    showQuickJumper: true,
    showTotal: total => `共：${tableModel.pagination.total} 条`,
    onChange(current, pageSize) {
      tableModel.pagination.pageSize = pageSize
      tableModel.pagination.current = current
      tableModel.loadDataSource()
    },
  })
})

const searchFormModel = reactive<any>({
})

const onReset = async () => {
  searchFormModel.S_EQ_t_article__art_id = undefined
  searchFormModel.expiryDays = undefined
  searchFormModel.keyword = undefined
  searchFormModel.S_EQ_t_article__cat_type_id = undefined
  searchFormModel.S_EQ_t_article__Subtype_ID = undefined
  searchFormModel.cellsTotalLe = undefined
  await onSearch()
}

const onSearch = async () => {
  tableModel.pagination.current = 1
  tableModel.loadDataSource()
}
const deptLs = ref<any[]>([])

const getDeptLs = async () => {
  const { data } = await findAllDeptLsApi({
    isOperator: 1
  })
  deptLs.value = data
  if (data.length > 0) {
    searchFormModel.deptCode = data[0].deptCode
    onSearch()
  }
}
// const catTypeList = ref([])
// const getCatTypeLs = async () => {
//   const { data } = await findStockEnabledAllApi({})
//   catTypeList.value = data
// }

onMounted(async () => {
  try {
    searchFormModel.expiryDays = props?.expiryDays
    if (props.modal) {
      tableModel.columns = [{
        title: '序号',
        dataIndex: 'index',
        width: 60,
        align: 'center',
        fixed: 'left'
      }, {
        title: '品种ID',
        dataIndex: 'artId',
        width: 80,
        align: 'center',
        fixed: 'left'
      }, {
        title: '品名',
        dataIndex: 'artName',
        width: 180,
        ellipsis: true,
        align: 'left',
        fixed: 'left'
      }, {
        title: '规格',
        dataIndex: 'artSpec',
        width: 150,
        ellipsis: true,
        align: 'left'
      }, {
        title: '库存量',
        dataIndex: 'currentTotal',
        width: 120,
        align: 'right'
      }, {
        title: '有效期至',
        dataIndex: 'expiry',
        width: 120,
        align: 'right'
      }, {
        title: '临期天数',
        dataIndex: 'expiryDays',
        width: 120,
        align: 'right'
      }, {
        title: '厂家',
        dataIndex: 'producer',
        ellipsis: true,
        width: 200,
        align: 'left'
      }, {
        title: '整包单位',
        dataIndex: 'packUnit',
        width: 120,
        align: 'center'
      }, {
        title: '仓库总库存',
        dataIndex: 'deptTotalPacks',
        width: 120,
        align: 'right'
      }, {
        title: '整包成本单价',
        dataIndex: 'packPrice',
        width: 120,
        align: 'right'
      }, {
        title: '拆零成本单价',
        dataIndex: 'cellPrice',
        width: 120,
        align: 'right'
      }, {
        title: '拆零销售单价',
        dataIndex: 'cellPrice',
        width: 120,
        align: 'right'
      }, {
        title: '生产日期',
        dataIndex: 'dateManufactured',
        width: 120,
        align: 'right'
      }, {
        title: '批准文号',
        dataIndex: 'approvalNo',
        width: 120,
        align: 'left'
      }, {
        title: '允许拆零',
        dataIndex: 'splittable',
        width: 80,
        align: 'center',
        customRender: ({ text }: { text: number }) => {
          return text ? text === 1 ? '是' : '' : ''
        },
      }, {
        title: '医保编码',
        dataIndex: 'miCode',
        width: 240,
        align: 'left'
      }, {
        title: '包装材质',
        dataIndex: 'packMaterial',
        width: 150,
        ellipsis: true,
        align: 'left'
      }, {
        title: '剂型名称',
        dataIndex: 'dosageForm',
        width: 120,
        align: 'left'
        // }, {
        //   title: '包装单位',
        //   dataIndex: 'packUnit',
        //   width: 120,
        //   align: 'left'
        // }, {
        //   title: '制剂单位',
        //   dataIndex: 'cellUnit',
        //   width: 120,
        //   align: 'left'
      }, {
        title: '货位编码',
        dataIndex: 'rackNo',
        width: 90,
        align: 'center'
      }, {
        title: '批次',
        dataIndex: 'stockNo',
        width: 120,
        align: 'right'
      }]
    }
  } catch (e) { }
  await getDeptLs()
  // await getCatTypeLs()
  await onSearch()
})

const onDownloadExcel = async () => {
  const res: any = await downloadExcelApi({
    ...searchFormModel
  })
  help.expExcel(res, '库存明细数据')
}

const batchExpiryUpdateVisible = ref(false)
const batchExpiryUpdateRef = ref()
const handleEditBatch = (record) => {
  batchExpiryUpdateVisible.value = true
  batchExpiryUpdateRef.value?.init(record)
}
const onCloseForm = () => {
  batchExpiryUpdateVisible.value = false
  tableModel.loadDataSource()
}

// WholeBox 拆零盒整相关
const wholeBoxRef = ref()

// 处理拆零盒整操作
const handleSplitPack = (record) => {
  if (!wholeBoxRef.value) {
    console.error('WholeBox组件未加载')
    return
  }

  // 构造拆零盒整所需的数据结构
  const splitPackData = {
    orgId: record.orgId,
    deptCode: record.deptCode,  
    artId: record.artId,
    artName: record.artName,
    artSpec: record.artSpec,
    producer: record.producer,
    packCells: record.packCells,
    cellUnit: record.cellUnit,
    packUnit: record.packUnit,
    deptTotalPacks: record.deptTotalPacks || 0,
    deptTotalCells: record.deptTotalCells || 0,
    totalPacks: record.totalPacks || 0,
    totalCells: record.totalCells || 0,
    batchNo: record.batchNo,
    expDate: record.expiry,
    stockNo: record.stockNo
  }
  console.log("splitPackData入参",splitPackData)

  // 调用拆零盒整组件
  wholeBoxRef.value.handleSplitPack(splitPackData)
  batchExpiryUpdateRef.value?.init(record)
}

// 拆零盒整成功回调
const handleSplitPackSuccess = () => {
  console.log('拆零盒整操作成功:','12313131')
  // 刷新表格数据
  tableModel.loadDataSource()
}

// 拆零盒整失败回调
const handleSplitPackError = (error) => {
  console.error('拆零盒整操作失败:', error)
}
const tableSummary = computed(() => {
  if (!tableModel.sumRow) {
    console.log('数据未加载，跳过计算');
    return null;
  }
  return [{
    index: 0,
    colSpan: 1,
    label: '合计：',
    style: {}
  }, {
    index: props.modal ? 1 : 22,
    colSpan: 1,
    label: props.modal ? '' : tableModel.sumRow.costAmount,
    style: {
      textAlign: 'right'
    }
  }, {
    index: props.modal? 2 : 23,
    colSpan: 1,
    label: props.modal ? '' : tableModel.sumRow.saleAmount,
    style: {
      textAlign: 'right'
    }
  }]
})
</script>
<template>
  <div class="bg-fff box-shadow p-16px h-full">
    <base-table :loading="tableModel.loading" :columns="tableModel.columns" :dataSource="tableModel.dataSource"
      :rowKey="(item: any) => item.artId + '-' + item.stockNo" :pagination="tableModel.pagination" :summary="props.modal ? undefined : tableSummary">
      <template #btns>
        <a-form :model="searchFormModel" w-full>
          <a-row p-t-2>
            <a-col flex="200px">
              <a-form-item label="仓库" name="deptCode">
                <a-select v-model:value="searchFormModel.deptCode" placeholder="请选择仓库" style="min-width: 120px;" @change="onSearch">
                  <a-select-option v-for="item in deptLs" :key="item.deptCode" :value="item.deptCode">{{ item.deptName }}</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col flex="180px">
              <a-form-item label="临期天数">
                <a-input-number v-model:value="searchFormModel.expiryDays" allow-clear @pressEnter="onSearch" />
              </a-form-item>
            </a-col>
            <a-col flex="180px">
              <a-form-item label="条目ID" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                <a-input v-model:value="searchFormModel.S_EQ_t_article__art_id" allow-clear @pressEnter="onSearch" />
              </a-form-item>
            </a-col>
            <a-col flex="250px">
              <a-form-item label="品名" :label-col="{ span: 4 }" :wrapper-col="{ span: 19 }">
                <a-input v-model:value="searchFormModel.keyword" allow-clear @pressEnter="onSearch"/>
              </a-form-item>
            </a-col>
            <a-col flex="200px">
              <a-form-item label="条目分类" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                <StockReqCat v-model="searchFormModel.S_EQ_t_article__cat_type_id" type="Select" @change="onSearch" />
              </a-form-item>
            </a-col>
            <a-col flex="200px">
              <a-form-item label="条目亚类" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                <ArtSubTypeDict v-model="searchFormModel.S_EQ_t_article__Subtype_ID" type="Select" @change="onSearch" />
              </a-form-item>
            </a-col>
            <a-col flex="180px">
              <a-button type="primary" :loading="tableModel.loading" @click="onSearch" m-l-2>
                查询
              </a-button>
              <a-button @click="onReset" m-l-2>
                重置
              </a-button>
            </a-col>
            <a-col flex="auto">
            </a-col>
            <a-col flex="100px">
              <a-button :loading="tableModel.loading" @click="onDownloadExcel">
                导出
              </a-button>
            </a-col>
          </a-row>
        </a-form>
      </template>
      <template #bodyCell="{ column, record, index }">
        <template v-if="column?.dataIndex === 'index'">
          {{ index + 1 }}
        </template>
        <!--          <template v-if="column?.dataIndex === 'artName'">-->
        <!--            {{ record.artName }} {{ record.artSpec }}<br/>{{ record.producer }}-->
        <!--          </template>-->
        <template v-if="column?.dataIndex === 'packUnit'">
          {{ record.packCells }} {{ record.cellUnit }} / {{ record.packUnit }}
        </template>
        <template v-if="column?.dataIndex === 'deptTotalPacks'">
          <span v-if="record.deptTotalPacks">{{ record.deptTotalPacks }}{{ record.packUnit }}</span><span
            v-if="record.deptTotalCells">{{ record.deptTotalCells }}{{ record.cellUnit }}</span>
        </template>
        <template v-if="column?.dataIndex === 'currentTotal'">
          <span v-if="record.totalPacks">{{ record.totalPacks }}{{ record.packUnit }}</span><span
            v-if="record.totalCells">{{ record.totalCells }}{{ record.cellUnit }}</span>
        </template>
        <template v-if="column?.dataIndex === 'safeTotal'">
          <span v-if="record.safeTotalPacks">{{ record.safeTotalPacks }}{{ record.packUnit }}</span><span
            v-if="record.safeTotalCells">{{ record.safeTotalCells }}{{ record.cellUnit }}</span>
        </template>
        <template v-if="column?.dataIndex === 'lastRestCells'">
          <span v-if="record.lastRestTotalPacks">{{ record.lastRestTotalPacks }}{{ record.packUnit }}</span><span
            v-if="record.lastRestTotalCells">{{ record.lastRestTotalCells }}{{ record.cellUnit }}</span>
        </template>
        <template v-if="column?.dataIndex === 'action'">
          <a-space>
             <a @click="handleSplitPack(record)" v-if="record.totalCells>=record.packCells">拆零盒整</a>
            <a @click="handleEditBatch(record)">修改效期</a>
          </a-space>
        </template>
      </template>
    </base-table>
    <batch-expiry-update ref="batchExpiryUpdateRef" v-model:visible="batchExpiryUpdateVisible" width="800px" @close="onCloseForm"/>

    <!-- WholeBox 拆零盒整组件 -->
    <WholeBox
      ref="wholeBoxRef"
      @split-pack-success="handleSplitPackSuccess"
      @split-pack-error="handleSplitPackError"
    />
  </div>

</template>
<style lang="less" scoped>
.bg-fff {
  background-color: #fff;
  // padding: 8px 0 0 8px;

  :deep(.ant-tabs-nav) {
    margin-bottom: 0;
  }

  :deep(.ant-tabs-nav-list) {
    margin-left: 10px;
  }
}
</style>
