<!--药房-库房业务-药房发药-门诊发药/住院发药-患者-处方列表-->
<script setup lang="ts">
import type {PaginationProps} from 'ant-design-vue'
import filters from '@/utils/filters'
import { pendingPageApi } from '~/api/clinics_wm/wmreq.ts'
import { recipeInfoApi } from '~/api/hsd/recipe.ts'
// import TrackCodeCollect from '@/pages/clinics-wm/comp/track_code_collect.vue'
import { RecipeTrackCode as TrackCodeCollect } from '@mh-wm/recipe-track-code'
import '@mh-wm/recipe-track-code/index.css'
import PrintTemplate from '@/components/print/index.vue'
import { SystemSettingEnum } from '@/enums/system-setting-enum.ts'

const userStore = useUserStore()
const trackCodeBatchInput = computed(() => {
  const enable = userStore.getSystemSettingByCode(SystemSettingEnum.CW_TRACK_CODE_VALIDATOR_PREFIX7)
  return enable && Number(enable) === 1
})

const emit = defineEmits(['selectedRecipe'])
const message = useMessage()
const props = defineProps({
  deptCode: { type: String, required: true },
  visitId: { type: Number, required: true },
  finishedFlag: { type: Number },
})
// 加载数据
interface TableModel {
  loading?: Boolean,
  columns: any[],
  dataSource: any[],
  selectedRowKeys: any[]
}

const recipeTableModel = reactive<TableModel>({
  loading: false,
  columns: [{
    // title: '序号',
    // dataIndex: 'index',
    // width: 50,
    // align: 'right'
  // }, {
  //   title: '患者姓名',
  //   dataIndex: 'patientName',
  //   width: 90,
  //   align: 'center'
  // }, {
    title: '处方号',
    dataIndex: 'rxNo',
    width: 140,
    align: 'center',
  }, {
    title: '处方科室',
    dataIndex: 'applyDeptname',
    width: 90,
    align: 'center'
  }, {
    title: '处方医生',
    dataIndex: 'clinicianName',
    width: 75,
    align: 'center'
  }, {
    title: '申请时间',
    dataIndex: 'timeApplied',
    width: 100,
    align: 'center'
  }, {
    title: '付数',
    dataIndex: 'times',
    width: 60,
    align: 'right'
  }, {
    title: '处方ID',
    dataIndex: 'recipeId',
    width: 75,
    align: 'right'
  }, {
    title: '搭配单打印时间',
    dataIndex: 'timePrinted',
    width: 100,
    align: 'right'
  }, {
    title: '诊疗类型',
    dataIndex: 'clinicTypeId',
    width: 100
  }, {
    title: '说明备注',
    dataIndex: 'notes',
    width: 200,
    align: 'left'
  }, {
    title: '操作',
    dataIndex: 'action',
    width: 120,
    fixed: 'right',
    align: 'center'
  }],
  dataSource: [],
  loadDataSource: async () => {
    recipeTableModel.loading = true
    try {
      const {data} = await pendingPageApi({
        finishedFlag: props.finishedFlag,
        deptCode: props.deptCode,
        S_EQ_t_recipe__Visit_Id: props.visitId,
        S_EQ_t_wm_req__Req_Type: 3,
        sidx: 't_wm_req.Time_Applied',
        order: 'asc',
        pageNum: recipeTableModel.pagination.current,
        pageSize: recipeTableModel.pagination.pageSize,
      })
      recipeTableModel.dataSource = data.list
      recipeTableModel.pagination.total = data.total ?? 0
      recipeTableModel.loading = false
    } catch (err) {
      console.log(err)
      recipeTableModel.loading = false
    }
  },
  selectedRowKeys: [],
  pagination: reactive<PaginationProps>({
    pageSize: 10,
    current: 1,
    total: 0,
    showQuickJumper: true,
    showTotal: total => `共：${recipeTableModel.pagination.total} 条`,
    onChange(current, pageSize) {
      recipeTableModel.pagination.pageSize = pageSize
      recipeTableModel.pagination.current = current
      recipeTableModel.loadDataSource()
    },
  }),
  customRow: (record) => {
    return {
      style: {
        color: record.relativeWbseqid ? 'red' : '',
      },
      onClick: () => {
        emit('selectedRecipe', record.visitId, record.recipeId)
      },
      onDblclick: () => {
        handleVisibleRecipe(record.recipeId)
      }
    }
  }
})
onMounted(async () => {
  recipeTableModel.loadDataSource()
})
const recipe = ref({})
const printTemplateRef = ref()

const handleVisibleRecipe = (recipeId: any) => {
  // console.log(recipeId)
  recipe.value = {}
  recipeInfoApi({ recipeId: recipeId }).then((res: any) => {
    recipe.value = res.data
    printTemplateRef.value?.init('recipe', [recipe.value])
  })
}
const trackCodeCollectRef = ref()
const trackCodeCollectVisible = ref(false)
const handleVisibleTrackCode = (item: any) => {
  trackCodeCollectVisible.value = true
  trackCodeCollectRef.value.init('处方号:' + item.rxNo + ' ' + item.patientName, item.wbSeqid, null, false)
}
</script>
<template>
  <base-table :loading="recipeTableModel.loading" :columns="recipeTableModel.columns" :dataSource="recipeTableModel.dataSource"
              rowKey="wmReqid" :scroll="{ x: 1000 }"
              :custom-row="recipeTableModel.customRow" :pagination="recipeTableModel.pagination">
    <template #bodyCell="{ column, record, index, text }">
      <template v-if="column?.dataIndex === 'name'">
        <a-button type="link">{{ record.name }}</a-button>
      </template>
      <template v-if="column?.dataIndex === 'index'">
        {{ index + 1 }}
      </template>
      <template v-if="['timePrinted', 'timeApplied'].includes(column.dataIndex)">
        {{ filters.dateFormatMDHM(text) }}
      </template>
      <template v-if="column?.dataIndex === 'status'">
        <span v-if="record.status === 0">未处理</span>
        <span v-if="record.status === 1">部分锁定</span>
        <span v-if="record.status === 2">全部锁定</span>
        <span v-if="record.status === 3">已出库</span>
      </template>
      <template v-if="column?.dataIndex === 'clinicTypeId'">
        <span v-if="record.clinicTypeId === 1">门诊</span>
        <span v-if="record.clinicTypeId === 2">住院</span>
        <span v-if="record.clinicTypeId === 3">急诊</span>
        <span v-if="record.clinicTypeId === 4">体检</span>
        <span v-if="record.clinicTypeId === 5">检测</span>
        <span v-if="record.clinicTypeId === 6">外部处方处置</span>
        <span v-if="record.clinicTypeId === 9">其他</span>
      </template>
      <template v-if="column?.dataIndex === 'action'">
        <a-space>
          <a @click="handleVisibleRecipe(record.recipeId)">处方</a>
          <a @click="handleVisibleTrackCode(record)">追溯码</a>
        </a-space>
      </template>
    </template>
  </base-table>

<!--  <track-code-collect ref="trackCodeCollectRef" v-model:visible="trackCodeCollectVisible" width="95%" @close="trackCodeCollectVisible = false" />-->
  <track-code-collect ref="trackCodeCollectRef" @cancel="trackCodeCollectVisible = false" :onlyAddRecognizedTrackCode="trackCodeBatchInput" :enableOnlyAddRecognizedTrackCodeOption="trackCodeBatchInput" />
  <!-- 打印 -->
  <printTemplate ref="printTemplateRef" />
</template>
<style lang="less" scoped>
.bg-fff {
  background-color: #fff;
  // padding: 8px 0 0 8px;

  :deep(.ant-tabs-nav) {
    margin-bottom: 0;
  }

  :deep(.ant-tabs-nav-list) {
    margin-left: 10px;
  }
}
</style>
