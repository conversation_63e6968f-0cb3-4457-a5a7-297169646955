<!--药房-库房业务-药房发药-门诊发药/住院发药-患者列表-->
<script setup lang="ts">
import { QuestionCircleOutlined } from '@ant-design/icons-vue'
import dayjs from "dayjs";
import type { PaginationProps } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { createVNode } from 'vue';
import { Modal } from 'ant-design-vue';
import {
  pendingListApi,
  pendingPageApi,
  pendingRecipeGroupByVisitPageApi,
  reqDeliverByVisitIdApi
} from '~/api/clinics_wm/wmreq.ts'
import { listApi as findAllDeptLsApi } from "~/api/clinics_wm/deptcustmap.ts";
import RecipeTable from "./recipe-table.vue";
import DetailTable from "./detail-table.vue";
import TrackCodeCollect from '@/pages/clinics-wm/comp/track_code_collect.vue'
import PrintTemplate from "~/components/print/index.vue";
import { recipeInfoApi } from "~/api/hsd/recipe.ts";
import { compileStyle } from 'vue/compiler-sfc';
import { SystemSettingEnum } from '@/enums/system-setting-enum.ts'

const userStore = useUserStore()
const trackCodeBatchInput = computed(() => {
  const enable = userStore.getSystemSettingByCode(SystemSettingEnum.CW_TRACK_CODE_VALIDATOR_PREFIX7)
  return enable && Number(enable) === 1
})

const message = useMessage()
const props = defineProps({
  clinicTypeId: { type: Number, required: true },
  cancel: { type: Boolean, default: false }
})


const printRecipe = import.meta.env.VITE_APP_PRINT_RECIPE == 1
const printSendMedication = import.meta.env.VITE_APP_PRINT_SENDMEDICATION == 1


// 1-门诊,2-住院,3-急诊,4-体检,5-检测,6-外部处方处置,9-其他
// const clinicTypeIdList = ref([{
//   label: '门诊',
//   value: 1
// }, {
//   label: '住院',
//   value: 2
// }, {
//   label: '急诊',
//   value: 3
// }, {
//   label: '体检',
//   value: 4
// }, {
//   label: '检测',
//   value: 5
// }, {
//   label: '外部处方处置',
//   value: 6
// }, {
//   label: '其他',
//   value: 9
// }])

// 加载数据
interface TableModel {
  loading?: Boolean,
  columns: any[],
  dataSource: any[],
  loadDataSource: Function,
  selectedRowKeys: any[],
  pagination?: any,
  currentRecord?: any,
  sumAmount?: any,
  customRow: Function
}
const searchFormModel = reactive<any>({})

const tableModel = reactive<TableModel>({
  loading: false,
  columns: [{
    //   title: '序号',
    //   dataIndex: 'index',
    //   width: 50,
    //   align: 'right'
    // }, {
    //   title: '诊疗ID',
    //   dataIndex: 'visitId',
    //   width: 50,
    //   align: 'center'
    // }, {
    title: '患者姓名',
    dataIndex: 'patientName',
    width: 100,
    align: 'center'
    // }, {
    //   title: '处方数',
    //   dataIndex: 'recipeCount',
    //   width: 70,
    //   align: 'right'
    // }, {
    //   title: '性别',
    //   dataIndex: 'genderName',
    //   width: 70,
    //   align: 'center'
    // }, {
    //   title: '年龄',
    //   dataIndex: 'ageOfYears',
    //   width: 70,
    //   align: 'center'
  }, {
    title: '操作',
    dataIndex: 'action',
    width: 120,
    // fixed: 'right',
    align: 'center'
  }],
  dataSource: [],
  sumAmount: { totalAmount: '0.00' },
  loadDataSource: async () => {
    tableModel.loading = true
    try {
      const { data } = await pendingRecipeGroupByVisitPageApi({
        ...searchFormModel,
        S_EQ_t_wm_req__Req_Type: 3,
        S_GE_t_wm_req__Time_Distributed: searchFormModel.finishedFlag === 1 ? searchFormModel.dateRange[0].format('YYYY-MM-DD') : null,
        S_LT_t_wm_req__Time_Distributed: searchFormModel.finishedFlag === 1 ? searchFormModel.dateRange[1].add(1, 'd').format('YYYY-MM-DD') : null,
        S_EQ_T_WM_REQ__Clinic_Type_ID: props.clinicTypeId,
        sidx: 't_recipe.Visit_ID',
        order: 'asc',
        pageNum: tableModel.pagination.current,
        pageSize: tableModel.pagination.pageSize,
      })
      tableModel.dataSource = data.list
      tableModel.pagination.total = data.total ?? 0

      // 计算总金额
      const totalAmount = data.list.reduce((sum, record) => {
        return sum + (Number(record.amount) || 0)
      }, 0).toFixed(2)

      // 存储总金额
      tableModel.sumAmount = {
        totalAmount: totalAmount
      }

      tableModel.loading = false
      tableModel.currentRecord = {}
    } catch (err) {
      console.log(err)
      tableModel.loading = false
    }
  },
  selectedRowKeys: [],
  pagination: reactive<PaginationProps>({
    pageSize: 10,
    current: 1,
    total: 0,
    showQuickJumper: true,
    showTotal: total => `共：${tableModel.pagination.total} 条`,
    onChange(current, pageSize) {
      tableModel.pagination.pageSize = pageSize
      tableModel.pagination.current = current
      tableModel.loadDataSource()
    },
  }),
  currentRecord: {},
  customRow: (record) => {
    return {
      style: {
        color: record.relativeWbseqid ? 'red' : '',
      },
      onClick: () => {
        searchFormModel.recipeId = null
        tableModel.currentRecord = record
      }
    }
  }
})

const deptLs = ref<any[]>([])
const getDeptLs = async () => {
  const { data } = await findAllDeptLsApi({
    isScheduler: 1,
    forRecipeDispensing: 1
  })
  deptLs.value = data
  if (data.length > 0) {
    searchFormModel.deptCode = data[0].deptCode
  }
}
onMounted(async () => {
  searchFormModel.dateRange = [dayjs(), dayjs()]
  searchFormModel.finishedFlag = 0
  await getDeptLs()
  await searchTableData()
})
// onActivated(async () => {
//   await searchTableData()
// })
const searchTableData = async () => {
  tableModel.pagination.current = 1
  await tableModel.loadDataSource()
}
const rangePresets = ref([
  { label: '当天', value: [dayjs(), dayjs()] },
  { label: '昨天', value: [dayjs().add(-1, 'd'), dayjs().add(-1, 'd')] },
  { label: '前天', value: [dayjs().add(-2, 'd'), dayjs().add(-2, 'd')] },
  { label: '最近3日', value: [dayjs().add(-2, 'd'), dayjs()] },
  { label: '最近7日', value: [dayjs().add(-6, 'd'), dayjs()] },
  { label: '最近14日', value: [dayjs().add(-13, 'd'), dayjs()] },
  { label: '最近30日', value: [dayjs().add(-29, 'd'), dayjs()] }
])

// 发药确认
const onDeliver = async (record) => {
  await reqDeliverByVisitIdApi({
    visitId: record.visitId,
    deptCode: record.deptCode
  })
  message.success('操作成功')
  await searchTableData()
  currentRecord.value = null
}
const trackCodeCollectRef = ref()
const trackCodeCollectVisible = ref(false)

const currentRecord = ref(null)
const handleTrackCodeTip = async (record) => {
  currentRecord.value = record
  const { data } = await pendingPageApi({
    finishedFlag: searchFormModel.finishedFlag,
    deptCode: searchFormModel.deptCode,
    S_EQ_t_recipe__Visit_Id: record.visitId,
    S_EQ_t_wm_req__Req_Type: 3,
    S_IN_t_wm_bill__Trackcode_Status: '0,1', // 未采集，采集中
    S_ISNULL_t_wm_bill__Relative_WBSeqID: 1, // 未退药
    sidx: 't_wm_req.Time_Applied',
    order: 'asc'
  })
  if (data.total > 0) {
    const item = data.list[0]
    Modal.confirm({
      title: '追溯码扫描确认?',
      icon: createVNode(ExclamationCircleOutlined),
      content: createVNode('div', {}, '当前处方还未进行追溯码上传，是否立即扫描？'),
      onOk() {
        trackCodeCollectVisible.value = true
        trackCodeCollectRef.value.init('处方号:' + item.rxNo, item.wbSeqid, record.visitId, false)
      },
      onCancel() {
        // 不扫码，直接发药确认
        onDeliver(record)
      },
      class: 'test',
    });
  } else {
    onDeliver(record)
  }
}
const handleTrackCode = async (visitId) => {
  const { data } = await pendingPageApi({
    finishedFlag: searchFormModel.finishedFlag,
    deptCode: searchFormModel.deptCode,
    S_EQ_t_recipe__Visit_Id: visitId,
    S_EQ_t_wm_req__Req_Type: 3,
    S_IN_t_wm_bill__Trackcode_Status: '0,1', // 未采集，采集中
    S_ISNULL_t_wm_bill__Relative_WBSeqID: 1, // 未退药
    sidx: 't_wm_req.Time_Applied',
    order: 'asc'
  })
  if (data.total > 0) {
    const item = data.list[0]
    console.log(item)
    trackCodeCollectVisible.value = true
    trackCodeCollectRef.value.init('处方号:' + item.rxNo + ' ' + item.patientName, item.wbSeqid, visitId, false)
  } else {
    if (searchFormModel.finishedFlag === 1) {
      message.warning('所有单据都已经提交')
    } else {
      onDeliver(currentRecord.value)
    }
  }
}
const onCloseTrackCodeCollect = async () => {
  trackCodeCollectVisible.value = false
}
const onOkTrackCodeCollect = async (visitId) => {
  trackCodeCollectVisible.value = false
  handleTrackCode(visitId)
}

const printTemplateRef = ref()
const recipes = ref([])
const handlePrint = async (record, printType = 'recipe') => {
  const { data } = await pendingListApi({
    finishedFlag: searchFormModel.finishedFlag,
    deptCode: searchFormModel.deptCode,
    S_EQ_t_recipe__Visit_Id: record.visitId,
    S_EQ_t_wm_req__Req_Type: 3,
    sidx: 't_wm_req.Time_Applied',
    order: 'asc'
  })
  const recipeIds = []
  data.forEach(item => {
    recipeIds.push(item.recipeId)
  })
  // console.log('record', record)
  recipeInfoApi({ recipeIds: recipeIds }).then((res: any) => {
    printTemplateRef.value?.init(printType, res.data)
  })
}
// 合计
const totalAmount = computed(() => {
  return [{
    index: 0,
    colSpan: 2,
    label: tableModel.sumAmount?.totalAmount || '0.00',
    style: {
      textAlign: 'right',
      color: '#0e0d0d',
      fontWeight: 'bold'
    }
  }]
})
const selectedRecipe = (visitId, recipeId) => {
  if (tableModel.currentRecord && tableModel.currentRecord.visitId === visitId) {
    searchFormModel.recipeId = recipeId
  } else {
    // message.warning('请先选择患者')
  }
}
</script>
<template>
  <a-form :model="searchFormModel" w-full layout="inline">
    <a-form-item label="仓库" name="deptCode">
      <a-select v-model:value="searchFormModel.deptCode" placeholder="请选择仓库" @change="searchTableData">
        <a-select-option v-for="item in deptLs" :key="item.deptCode" :value="item.deptCode">{{ item.deptName
        }}</a-select-option>
      </a-select>
    </a-form-item>
    <a-form-item label="" name="finishedFlag">
      <a-radio-group v-model:value="searchFormModel.finishedFlag" @change="searchTableData">
        <a-radio :value="0">待发药</a-radio>
        <a-radio :value="1">已发药</a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item label="发药日期" name="startDate" v-if="searchFormModel.finishedFlag === 1">
      <a-range-picker :presets="rangePresets" v-model:value="searchFormModel.dateRange" @change="searchTableData"
        :allow-clear="false" />
    </a-form-item>
    <a-form-item label="患者姓名" name="S_LIKE_t_visit__Patient_Name">
      <a-input v-model:value="searchFormModel.S_LIKE_t_visit__Patient_Name" placeholder="请输入患者姓名"
        @pressEnter="searchTableData" />
    </a-form-item>
    <a-form-item label="处方号" name="S_LIKE_t_recipe__rx_no">
      <a-input v-model:value="searchFormModel.S_LIKE_t_recipe__rx_no" placeholder="请输入处方号"
        @pressEnter="searchTableData" />
    </a-form-item>
    <!--    <a-form-item label="诊疗类型" name="S_EQ_T_WM_REQ__Clinic_Type_ID">-->
    <!--      <a-select v-model:value="searchFormModel.S_EQ_T_WM_REQ__Clinic_Type_ID" placeholder="请选择诊疗类型" @change="searchTableData" style="width: 130px" allow-clear>-->
    <!--        <a-select-option v-for="item in clinicTypeIdList" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>-->
    <!--      </a-select>-->
    <!--    </a-form-item>-->
    <a-button type="primary" :loading="tableModel.loading" @click="searchTableData">
      查询
    </a-button>
  </a-form>
  <a-row>
    <a-col :span="6">
      <base-table :loading="tableModel.loading" :columns="tableModel.columns" :dataSource="tableModel.dataSource"
        rowKey="visitId" :scroll="{ y: 'calc(100vh - 350px)' }" :custom-row="tableModel.customRow"
        :pagination="tableModel.pagination" :summary="totalAmount">
        <template #bodyCell="{ column, record, index }">
          <template v-if="column?.dataIndex === 'index'">
            {{ index + 1 }}
          </template>
          <template v-if="column?.dataIndex === 'patientName'">
            {{ record.patientName }} {{ record.genderName }}
            <span v-if="record.ageOfYears > 0">{{ record.ageOfYears }} 岁</span>
            <span v-else>{{ record.ageOfDays }} 天</span>
            ({{ record.recipeCount }})
            <br />
            <span class="price-tag">￥{{ record.amount }}</span>
          </template>
          <template v-if="column?.dataIndex === 'ageOfYears'">
            <span v-if="record.ageOfYears > 0">{{ record.ageOfYears }} 岁</span>
            <span v-else>{{ record.ageOfDays }} 天</span>
          </template>
          <template v-if="column?.dataIndex === 'action'">
            <a-space>
              <a v-if="printRecipe" @click="handlePrint(record, 'recipe')">打印处方</a>
              <a v-if="printSendMedication" @click="handlePrint(record, 'sendMedication')">打印发药单</a>
              <a-popconfirm v-if="record.status === 2" title="确定发药吗？" ok-text="确定" cancel-text="取消"
                @confirm="handleTrackCodeTip(record)">
                <template #icon><question-circle-outlined style="color: red" /></template>
                <a>
                  发药
                </a>
              </a-popconfirm>
              <a v-if="record.status === 3" @click="handleTrackCode(record.visitId)">追溯码采集</a>
            </a-space>
          </template>
        </template>
        <template #expandedRowRender="{ record }">
          <recipe-table :deptCode="record.deptCode" :visitId="record.visitId"
            :finishedFlag="searchFormModel.finishedFlag" @selectedRecipe="selectedRecipe" />
        </template>
      </base-table>
    </a-col>
    <a-col :span="18">
      <detail-table :deptCode="tableModel.currentRecord.deptCode" :visitId="tableModel.currentRecord.visitId"
        :recipeId="searchFormModel.recipeId" :finishedFlag="searchFormModel.finishedFlag" />
    </a-col>
  </a-row>
  <track-code-collect ref="trackCodeCollectRef" v-model:visible="trackCodeCollectVisible" width="95%"
    @close="onCloseTrackCodeCollect" @ok="onOkTrackCodeCollect" />
  <!-- 打印 -->
  <printTemplate ref="printTemplateRef" />
</template>
<style lang="less" scoped>
.bg-fff {
  background-color: #fff;
  // padding: 8px 0 0 8px;

  :deep(.ant-tabs-nav) {
    margin-bottom: 0;
  }

  :deep(.ant-tabs-nav-list) {
    margin-left: 10px;
  }
}

.price-tag {
  display: inline-block;
  color: #0e0d0d;

  border-radius: 4px;
  padding: 2px 8px;
  font-weight: bold;
}
</style>
