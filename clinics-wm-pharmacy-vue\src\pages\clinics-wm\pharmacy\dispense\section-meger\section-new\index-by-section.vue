<!--药房-库房业务-药房发药-病区发药-待发药-按病区发药-->
<script setup lang="ts">
import dayjs from "dayjs";
import type {PaginationProps} from 'ant-design-vue'
import {
  sectionMegerBySectionPageApi
} from '~/api/clinics_wm/wmreq.ts'
import OeReqForm from "./form.vue"


const message = useMessage()
const props = defineProps({
  deptCode: { type: String }
})

// 加载数据
interface TableModel {
  loading?: Boolean,
  columns: any[],
  dataSource: any[],
  selectedRowKeys: any[],
  pagination: any
}

const tableModel = reactive<TableModel>({
  loading: false,
  columns: [{
  //   title: '序号',
  //   dataIndex: 'index',
  //   width: 50,
  //   align: 'right'
  // }, {
    title: '病区ID',
    dataIndex: 'sectionId',
    width: 30,
    align: 'right'
  }, {
    title: '申领病区',
    dataIndex: 'sectionName',
    width: 75,
    align: 'center'
  }, {
    title: '单据数量',
    dataIndex: 'billCount',
    width: 60,
    align: 'right'
  }],
  dataSource: [],
  loadDataSource: async () => {
    tableModel.loading = true
    try {
      const {data} = await sectionMegerBySectionPageApi({
        ...searchFormModel,
        deptCode: props.deptCode,
        pageNum: tableModel.pagination.current,
        pageSize: tableModel.pagination.pageSize,
      })
      tableModel.dataSource = data.list
      tableModel.pagination.total = data.total ?? 0

      // 刷新事件后默认选中第一行数据
      if (data.list && data.list.length > 0) {
        const firstRecord = data.list[0]
        tableModel.selectedRowKeys = [firstRecord.sectionId]

        // 调用 oeReqFormRef 的 init 方法，传递第一行数据
        if (oeReqFormRef.value) {
          oeReqFormRef.value.init(
            props.deptCode,
            firstRecord.sectionId,
            firstRecord.sectionName,
            searchFormModel.finishedFlag
          )
        }
      } else {
        // 如果没有数据，清空选中状态并初始化空状态
        tableModel.selectedRowKeys = []
        if (oeReqFormRef.value) {
          oeReqFormRef.value.init(props.deptCode, null, null, searchFormModel.finishedFlag)
        }
      }

      tableModel.loading = false
    } catch (err) {
      console.log(err)
      tableModel.loading = false
    }
  },
  selectedRowKeys: [],
  pagination: reactive<PaginationProps>({
    pageSize: 10,
    current: 1,
    total: 0,
    showQuickJumper: true,
    showTotal: total => `共：${tableModel.pagination.total} 条`,
    onChange(current, pageSize) {
      tableModel.pagination.pageSize = pageSize
      tableModel.pagination.current = current
      tableModel.loadDataSource()
    },
  })
})

const searchFormModel = reactive<any>({})
const onSearchWmBill = async () => {
  tableModel.pagination.current = 1
  tableModel.loadDataSource()
}
// const deptLs = ref<any[]>([])
//
// const getDeptLs = async () => {
//   const { data } = await findAllDeptLsApi({
//     isScheduler: 1,
//     forSectionReq: 1
//   })
//   deptLs.value = data
//   if (data.length > 0) {
//     searchFormModel.deptCode = data[0].deptCode
//   }
// }
onMounted(async () => {
  searchFormModel.dateRange = [dayjs().add(-6, 'd'), dayjs()]
  searchFormModel.finishedFlag = 0
  // await getDeptLs()
  await onSearchWmBill()
})
const rangePresets = ref([
  {label: '当天', value: [dayjs(), dayjs()]},
  {label: '最近3日', value: [dayjs().add(-2, 'd'), dayjs()]},
  {label: '最近7日', value: [dayjs().add(-6, 'd'), dayjs()]},
  {label: '最近14日', value: [dayjs().add(-13, 'd'), dayjs()]},
  {label: '最近30日', value: [dayjs().add(-29, 'd'), dayjs()]}
])
const customRow = (record: any) => {
  return {
    onClick: () => {
      // 更新选中状态
      tableModel.selectedRowKeys = [record.sectionId]

      // 调用 oeReqFormRef 的 init 方法
      oeReqFormRef.value?.init(props.deptCode, record.sectionId, record.sectionName, searchFormModel.finishedFlag)
    }
  }
}

const oeReqFormRef = ref()
watch(props, async (newVal) => {
  await onSearchWmBill()
})
// watchEffect(async () => {
//   await onSearchWmBill()
// })
// const activeKey = ref('1')
</script>
<template>
  <a-row>
    <a-col :span="5">
<!--      <a-tabs v-model:activeKey="activeKey">-->
<!--        <a-tab-pane key="1" tab="待发药">-->
<!--          <a-form :label-col="{ span: 5 }" :wrapper-col="{ span: 18 }" :model="searchFormModel" w-full>-->
<!--            <a-row p-t-2>-->
<!--              <a-col :span="4">-->
<!--                <a-space>-->
<!--                  <a-button type="primary" :loading="tableModel.loading" @click="onSearchWmBill">-->
<!--                    查询-->
<!--                  </a-button>-->
<!--                </a-space>-->
<!--              </a-col>-->
<!--            </a-row>-->
<!--          </a-form>-->
<!--        </a-tab-pane>-->
<!--      </a-tabs>-->
      <base-table :loading="tableModel.loading" :columns="tableModel.columns" :dataSource="tableModel.dataSource"
                  :rowKey="(item: any) => item.sectionId"
                  :custom-row="customRow" :pagination="tableModel.pagination"
                  :row-selection="{
                    selectedRowKeys: tableModel.selectedRowKeys,
                    type: 'radio',
                    onChange: (selectedRowKeys: any[], selectedRows: any[]) => {
                      tableModel.selectedRowKeys = selectedRowKeys
                      if (selectedRows.length > 0) {
                        const record = selectedRows[0]
                        oeReqFormRef?.init(
                          props.deptCode,
                          record.sectionId,
                          record.sectionName,
                          searchFormModel.finishedFlag
                        )
                      }
                    }
                  }">
        <template #btns>
          <a-space>
            <a-button type="primary" :loading="tableModel.loading" @click="onSearchWmBill">
              刷新
            </a-button>
          </a-space>
        </template>
      </base-table>
    </a-col>
    <a-col :span="19">
      <oe-req-form ref="oeReqFormRef" width="1200px" @refresh="onSearchWmBill"/>
    </a-col>
  </a-row>


</template>
<style lang="less" scoped>
.bg-fff {
  background-color: #fff;

  :deep(.ant-tabs-nav) {
    margin-bottom: 0;
  }

  :deep(.ant-tabs-nav-list) {
    margin-left: 10px;
  }
}
</style>
