<!--药房-库房业务-药房发药-病区发药-按病区发药-已锁定明细-->
<script setup lang="ts">
import { ref, reactive, watchEffect, computed } from 'vue'
import {QuestionCircleOutlined} from '@ant-design/icons-vue'

import {
  infoApi, sectionMegerArtDetailPageApi, sectionMegerArtListApi, sectionMegerArtPageApi, sectionMegerPrintHeaderApi
    ,releaseStockByWmReqidsApi
} from "~/api/clinics_wm/wmreq.ts";
import type {PaginationProps} from "ant-design-vue";
import {getPrintTemplateFullApi, PrintTemplateType} from "~/api/hip/printtemplate.ts";
import lodop from "~/components/lodop";

interface TableModel {
  loading: boolean;
  columns: Array<{
    title: string;
    dataIndex: string;
    width?: number;
    align?: 'left' | 'right' | 'center';
  }>;
  dataSource: any[];
  dataSourceExtra: Record<string, any>;
  pagination: PaginationProps;
  customRow: (record: any) => { style: { color: string } };
  loadDataSource: () => Promise<void>;
}

const props = defineProps({
  deptCode: { type: String },
  sectionId: { type: Number },
  visitId: { type: Number },
})
const message = useMessage()
const emit = defineEmits(['ok', 'refresh', 'dataLoaded'])

const showShortFlag = ref(false)
const detailTableModel = reactive<TableModel>({
  loading: false,
  columns: [{
    title: '序号',
    dataIndex: 'index',
    width: 60,
    align: 'right'
  }, {
    title: '申请单',
    dataIndex: 'wmReqid',
    width: 80,
    align: 'right'
  }, {
    title: '锁定单',
    dataIndex: 'wbSeqid',
    width: 80,
    align: 'right'
  }, {
    title: '床号',
    dataIndex: 'bedNo',
    width: 60,
    align: 'right'
  }, {
    title: '患者',
    dataIndex: 'patientName',
    width: 80,
    align: 'left'
  }, {
    title: '品种ID',
    dataIndex: 'artId',
    width: 80,
    align: 'right'
  }, {
    title: '品名|规格|厂家',
    dataIndex: 'name',
    align: 'left'
  }, {
    title: '申领数量',
    dataIndex: 'shortCells',
    width: 75,
    align: 'right'
  }, {
    title: '已发药数',
    dataIndex: 'cellsDelivered',
    width: 75,
    align: 'right'
  }, {
    title: '已锁定数',
    dataIndex: 'cellsReserved',
    width: 75,
    align: 'right'
  // }, {
  //   title: '销售金额',
  //   dataIndex: 'amount',
  //   width: 75,
  //   align: 'right'
  }, {
    title: '医保编码',
    dataIndex: 'miCode',
    width: 150,
    align: 'left'
    // }, {
    //   title: '锁定状态',
    //   dataIndex: 'reservedFlag',
    //   width: 90,
    //   align: 'center'
    // }, {
    //   title: '货位编码',
    //   dataIndex: 'rackNo',
    //   width: 90,
    //   align: 'center'
  }],
  dataSource: [],
  dataSourceExtra: {},
  loadDataSource: async () => {
    if (props.deptCode && props.sectionId) {
      detailTableModel.loading = true
      try {
        // 构建API参数，visitId为可选参数
        const apiParams = {
          ...searchFormModel,
          deptCode: props.deptCode,
          sectionId: props.sectionId,
          lockedFlag: 2,
          showShortFlag: showShortFlag.value ? 1 : 0,
          pageNum: detailTableModel.pagination.current,
          pageSize: detailTableModel.pagination.pageSize,
        }

        // 只有当visitId存在时才添加到参数中
        if (props.visitId) {
          apiParams.visitId = props.visitId
        }

        const {data} = await sectionMegerArtDetailPageApi(apiParams)
        detailTableModel.dataSource = data.list
        detailTableModel.dataSourceExtra = data.extra || {}
        detailTableModel.pagination.total = data.total ?? 0
        detailTableModel.loading = false

        console.log('section-new 已锁定明细数据加载完成:', {
          dataCount: data.list?.length || 0,
          total: data.total,
          extra: data.extra,
          apiParams
        })

        // 发射数据加载完成事件
        emit('dataLoaded')
      } catch (err) {
        console.log(err)
        detailTableModel.loading = false
      }
    } else {
      detailTableModel.dataSource = []
      detailTableModel.dataSourceExtra = {}

      // 发射数据加载完成事件
      emit('dataLoaded')
    }
  },
  pagination: reactive<PaginationProps>({
    pageSize: 100,
    current: 1,
    total: 0,
    showQuickJumper: true,
    showSizeChanger: true,
    showTotal: total => `共：${detailTableModel.pagination.total} 条`,
    onChange(current, pageSize) {
      detailTableModel.pagination.pageSize = pageSize
      detailTableModel.pagination.current = current
      detailTableModel.loadDataSource()
    },
  }),
  customRow: (record) => {
    return {
      style: {
        color: record.shortCells > record.cellsDelivered + record.cellsReserved ? 'red' : '',
      }
    }
  }
})
const searchFormModel = reactive<any>({})

// 合计
const tableSummary = computed(() => {
  if (!detailTableModel.dataSourceExtra || Object.keys(detailTableModel.dataSourceExtra).length === 0) {
    return null
  }

  return [{
    index: 0,
    colSpan: 5,
    label: '合计：',
    style: {}
  }, {
    index: 5,
    colSpan: 1,
    label: detailTableModel.dataSourceExtra.shortCellsSum || '',
    style: {
      textAlign: 'right'
    }
  }, {
    index: 6,
    colSpan: 1,
    label: detailTableModel.dataSourceExtra.cellsDeliveredSum || '',
    style: {
      textAlign: 'right'
    }
  }, {
    index: 7,
    colSpan: 1,
    label: detailTableModel.dataSourceExtra.cellsReservedSum || '',
    style: {
      textAlign: 'right'
    }
  }]
})

watchEffect(async () => {
  await detailTableModel.loadDataSource()
})
const reload = async () => {
  detailTableModel.pagination.current = 1
  await detailTableModel.loadDataSource()
}

const clearData = () => {
  detailTableModel.dataSource = []
  detailTableModel.dataSourceExtra = {}
  detailTableModel.pagination.total = 0
  detailTableModel.pagination.current = 1

  // 发射数据加载完成事件
  emit('dataLoaded')
}

// const wmReqInfoRef = ref({})
// const loadWmReqInfo = async () => {
//   const { data } = await infoApi({
//     wmReqid: props.wmReqid
//   })
//   wmReqInfoRef.value = data
// }

const loadSectionMegerPrintHeader = async () => {
  const {data} = await sectionMegerPrintHeaderApi({
    deptCode: props.deptCode,
    sectionId: props.sectionId,
    visitId: props.visitId
  })
  return data
}
const handlePrintBill = async () => {
  const printHeader = await loadSectionMegerPrintHeader()
  const {data} = await sectionMegerArtListApi({
    deptCode: props.deptCode,
    sectionId: props.sectionId,
    S_EQ_t_wm_req_detail__visit_ID: props.visitId,
    allFlag: 1
  })
  const billData = {
    ...printHeader,
    details: data
  }
  const template = await getPrintTemplateFullApi(PrintTemplateType.section_meger_reqart)
  if (template.tempItems) {
    template.tempItems = JSON.parse(template.tempItems);
  }
  console.log(billData)
  const printData = [{
    ...billData
  }]
  lodop.preview(template, printData)
}
// 取消锁定
const releaseStockByWmReqids = async () => {
  detailTableModel.loading = true
  try {
    if (!detailTableModel?.dataSource) {
      throw new Error('要释放的申请单不能为空');
    }
    // 过滤 reqType=5 且 wmReqid 有效的数据，再提取 wmReqid
    const reqDetails = detailTableModel.dataSource
        .filter(item => item?.reqType !== 5 && item?.wmReqid != null)
        .map(item => ({
          wmReqid: item.wmReqid,
          lineNo: item.lineNo
        }));
    if (reqDetails.length === 0){
      throw new Error('请选择要释放的有效申请单,病区发药申请单无法取消锁定');
    }
    await releaseStockByWmReqidsApi({
      reqDetails: reqDetails
    })
    message.success('操作成功')
    emit('refresh'); // 刷新左侧表格
  } catch (err) {
    console.log(err)
    message.error(err.message || '库存锁定失败')
  } finally {
    detailTableModel.loading = false
  }
}

// 获取数据数量的方法
const getDataCount = () => {
  const count = detailTableModel.dataSource.length
  console.log('section-new 已锁定明细 getDataCount 被调用:', count)
  return count
}

defineExpose({
  reload,
  clearData,
  getDataCount
})
</script>
<template>
  <base-table :loading="detailTableModel.loading" :columns="detailTableModel.columns" :height="600"
              :dataSource="detailTableModel.dataSource" :pagination="detailTableModel.pagination" :custom-row="detailTableModel.customRow"
              :summary="tableSummary"
              :rowKey="(item: any) => item.wmReqid + '-' + item.lineNo">
    <template #btns>
      <a-space size="middle" class="m-l-10px">
        <a-space>
          <text>条目ID:</text>
          <a-input v-model:value="searchFormModel.S_EQ_t_wm_req_detail__Art_ID" style="width: 80px" @pressEnter="reload"/>
        </a-space>
        <a-space>
          <text>品名:</text>
          <a-input v-model:value="searchFormModel.S_LIKE_t_article__Art_Name" style="width: 150px" @pressEnter="reload"/>
        </a-space>
        <a-space>
          <text>患者:</text>
          <a-input v-model:value="searchFormModel.S_LIKE_t_visit__Patient_Name" style="width: 100px" @pressEnter="reload"/>
        </a-space>
<!--        <a-checkbox v-model:checked="showShortFlag" @change="reload">只显示未完成</a-checkbox>-->
        <a-popconfirm title="非病区发药药品的取消锁定，确定取消锁定吗？" ok-text="确定" cancel-text="取消" @confirm="releaseStockByWmReqids"
                      v-if="props.sectionId">
          <template #icon>
            <question-circle-outlined style="color: red"/>
          </template>
          <a-button type="primary" danger  v-if="props.sectionId">
            取消锁定
          </a-button>
        </a-popconfirm>
        <a-button @click="handlePrintBill" v-if="props.sectionId">
          打印申领汇总单
        </a-button>
      </a-space>
    </template>
    <template #bodyCell="{ column, record, index }">
      <template v-if="column?.dataIndex === 'index'">
        {{ index + 1 }}
      </template>
      <template v-if="column?.dataIndex === 'name'">
        {{ record.artName }} {{ record.artSpec }}<br/>{{ record.producer }}
      </template>
      <template v-if="column?.dataIndex === 'packCells'">
        {{ record.packCells }} {{ record.cellUnit }} / {{ record.packUnit }}
      </template>
      <!--      <template v-if="column?.dataIndex === 'totalPacks'">-->
      <!--        <span v-if="record.totalPacks">{{ record.totalPacks }}{{ record.packUnit }}</span><span v-if="record.totalCells">{{ record.totalCells }}{{ record.cellUnit }}</span>-->
      <!--      </template>-->
      <template v-if="column?.dataIndex === 'shortCells'">
        <span v-if="record.shortCells">{{ record.shortCells }}{{ record.cellUnit }}</span>
      </template>
      <template v-if="column?.dataIndex === 'cellsDelivered'">
        <span v-if="record.cellsDelivered > 0">{{ record.cellsDelivered }}{{ record.cellUnit }}</span>
      </template>
      <template v-if="column?.dataIndex === 'cellsReserved'">
        <span v-if="record.cellsReserved > 0">{{ record.cellsReserved }}{{ record.cellUnit }}</span>
      </template>
      <template v-if="column?.dataIndex === 'reservedFlag'">
        <span v-if="record.reservedFlag === 0">未锁定</span>
        <span v-if="record.reservedFlag === 1">完全锁定</span>
        <span v-if="record.reservedFlag === 2">部分锁定</span>
        <span v-if="record.reservedFlag === 3">完全缺货</span>
      </template>
    </template>
  </base-table>
</template>
<style lang="less" scoped>
.bg-fff {
  background-color: #fff;
  // padding: 8px 0 0 8px;

  :deep(.ant-tabs-nav) {
    margin-bottom: 0;
  }
  :deep(.ant-tabs-nav-list) {
    margin-left: 10px;
  }
}
</style>
