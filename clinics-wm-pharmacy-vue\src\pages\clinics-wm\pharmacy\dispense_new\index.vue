<!--药房-库房业务-药房发药-->
<script setup lang="ts">
import RecipeIndexByVisit from "./recipe-by-visit/index.vue";

// 显示住院发药
const showIpDispense = import.meta.env.VITE_APP_SHOW_IP_DISPENSE == 1

const activeKey = ref("2");
</script>
<template>
  <page-container>
    <div class="bg-fff box-shadow p-l-16px p-r-16px h-full">
      <a-tabs v-model:activeKey="activeKey">
<!--        <a-tab-pane key="1" tab="处方发药">-->
<!--          <recipe-dispense-index />-->
<!--        </a-tab-pane>-->
        <a-tab-pane key="2" tab="门诊发药">
          <div h-2 />
          <recipe-index-by-visit :clinicTypeId="1"/>
        </a-tab-pane>
        <a-tab-pane key="3" tab="住院发药" v-if="showIpDispense">
          <div h-2 />
          <recipe-index-by-visit :clinicTypeId="2"/>
        </a-tab-pane>
      </a-tabs>
    </div>
  </page-container>
</template>
<style lang="less" scoped>
.bg-fff {
  background-color: #fff;
  // padding: 8px 0 0 8px;

  :deep(.ant-tabs-nav) {
    margin-bottom: 0;
  }

  :deep(.ant-tabs-nav-list) {
    margin-left: 10px;
  }
}
</style>
