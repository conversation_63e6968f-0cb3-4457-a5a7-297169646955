<script setup lang="ts">

import {
  finishedReqBySectionApi,
  sectionMegerArtDetailPageApi,
  sectionMegerArtListApi,
  sectionMegerPrintHeaderApi,
  stockReserveBySectionApi
} from "~/api/clinics_wm/wmreq.ts";
import {Modal, PaginationProps} from "ant-design-vue";
import {getPrintTemplateFullApi, PrintTemplateType} from "~/api/hip/printtemplate.ts";
import lodop from "~/components/lodop";
import {createVNode} from "vue";
import {ExclamationCircleOutlined} from "@ant-design/icons-vue";

const props = defineProps({
  deptCode: { type: String },
  sectionId: { type: Number },
  visitId: { type: Number },
})

const emit = defineEmits(['ok', 'refresh', 'locked', 'dataLoaded'])

const message = useMessage()
const showShortFlag = ref(true)
const btnLoading = ref(false)
const detailTableModel = reactive<TableModel>({
  loading: false,
  columns: [{
    title: '序号',
    dataIndex: 'index',
    width: 60,
    align: 'right'
  }, {
    title: '申请单',
    dataIndex: 'wmReqid',
    width: 80,
    align: 'right'
  }, {
    title: '床号',
    dataIndex: 'bedNo',
    width: 60,
    align: 'right'
  }, {
    title: '患者',
    dataIndex: 'patientName',
    width: 80,
    align: 'left'
  }, {
    title: '品种ID',
    dataIndex: 'artId',
    width: 80,
    align: 'right'
  }, {
    title: '品名|规格|厂家',
    dataIndex: 'name',
    align: 'left'
  }, {
    title: '申领数量',
    dataIndex: 'shortCells',
    width: 75,
    align: 'right'
  }, {
    title: '已发药数',
    dataIndex: 'cellsDelivered',
    width: 75,
    align: 'right'
  // }, {
  //   title: '已锁定数量',
  //   dataIndex: 'cellsReserved',
  //   width: 90,
  //   align: 'right'
  }, {
    title: '销售金额',
    dataIndex: 'amount',
    width: 75,
    align: 'right'
  }, {
    title: '医保编码',
    dataIndex: 'miCode',
    width: 150,
    align: 'left'
    // }, {
    //   title: '锁定状态',
    //   dataIndex: 'reservedFlag',
    //   width: 90,
    //   align: 'center'
    // }, {
    //   title: '货位编码',
    //   dataIndex: 'rackNo',
    //   width: 90,
    //   align: 'center'
  }],
  dataSource: [],
  loadDataSource: async () => {
    if (props.deptCode && props.sectionId) {
      detailTableModel.loading = true
      try {
        const {data} = await sectionMegerArtDetailPageApi({
          ...searchFormModel,
          deptCode: props.deptCode,
          sectionId: props.sectionId,
          visitId: props.visitId,
          lockedFlag: 1,
          showShortFlag: showShortFlag.value ? 1 : 0,
          pageNum: detailTableModel.pagination.current,
          pageSize: detailTableModel.pagination.pageSize,
        })
        detailTableModel.dataSource = data.list
        detailTableModel.pagination.total = data.total ?? 0
        detailTableModel.loading = false

        // 发射数据加载完成事件
        emit('dataLoaded')
      } catch (err) {
        console.log(err)
        detailTableModel.loading = false
      }
    } else {
      detailTableModel.dataSource = []

      // 发射数据加载完成事件
      emit('dataLoaded')
    }
  },
  pagination: reactive<PaginationProps>({
    pageSize: 100,
    current: 1,
    total: 0,
    showQuickJumper: true,
    showSizeChanger: true,
    showTotal: total => `共：${detailTableModel.pagination.total} 条`,
    onChange(current, pageSize) {
      detailTableModel.pagination.pageSize = pageSize
      detailTableModel.pagination.current = current
      detailTableModel.loadDataSource()
    },
  }),
  customRow: (record) => {
    return {
      style: {
        color: record.shortCells > record.cellsDelivered + record.cellsReserved ? 'red' : '',
      }
    }
  }
})
const searchFormModel = reactive<any>({})

watchEffect(async () => {
  await detailTableModel.loadDataSource()
})
const reload = async () => {
  detailTableModel.pagination.current = 1
  await detailTableModel.loadDataSource()
}

const clearData = () => {
  detailTableModel.dataSource = []
  detailTableModel.pagination.total = 0
  detailTableModel.pagination.current = 1

  // 发射数据加载完成事件
  emit('dataLoaded')
}

// 库存锁定
const onReserve = async () => {
  btnLoading.value = true
  try {
    const { data } = await stockReserveBySectionApi({
      deptCode: props.deptCode,
      sectionId: props.sectionId,
    })
    // reservedWbSeqid.value = data
    message.success('操作成功')
    // await reload()
    // 通知父组件刷新其他表格
    // emit('ok')
    // 改成通知父组件已锁定，父组件切换待出库去发药
    emit('locked')
  } catch (err) {
    console.log(err)
    message.error('库存锁定失败')
  } finally {
    btnLoading.value = false
  }
}
const onFinishedReq = async () => {
  Modal.confirm({
    title: '驳回申请?',
    icon: createVNode(ExclamationCircleOutlined),
    content: createVNode('div', {}, '此操作会将当前病区所有的领用申请全部驳回，请确定不需要再发药了再操作!'),
    onOk () {
      finishedReq()
    },
    onCancel() {
    },
    class: 'test',
  });
}
const finishedReq = async () => {
  btnLoading.value = true
  try {
    await finishedReqBySectionApi({
      deptCode: props.deptCode,
      sectionId: props.sectionId,
    })
    message.success('操作成功')
    // 通知父组件刷新左侧表格
    emit('refresh')
  } catch (err) {
    console.error('结束请求失败:', err)
    message.error('结束请求失败')
  } finally {
    btnLoading.value = false
  }
}


// const wmReqInfoRef = ref({})
// const loadWmReqInfo = async () => {
//   const { data } = await infoApi({
//     wmReqid: props.wmReqid
//   })
//   wmReqInfoRef.value = data
// }

const loadSectionMegerPrintHeader = async () => {
  const {data} = await sectionMegerPrintHeaderApi({
    deptCode: props.deptCode,
    sectionId: props.sectionId,
    visitId: props.visitId
  })
  return data
}
const handlePrintBill = async () => {
  const printHeader = await loadSectionMegerPrintHeader()
  const {data} = await sectionMegerArtListApi({
    deptCode: props.deptCode,
    sectionId: props.sectionId,
    S_EQ_t_wm_req_detail__visit_ID: props.visitId,
    allFlag: 1
  })
  const billData = {
    ...printHeader,
    details: data
  }
  const template = await getPrintTemplateFullApi(PrintTemplateType.section_meger_reqart)
  if (template.tempItems) {
    template.tempItems = JSON.parse(template.tempItems);
  }
  console.log(billData)
  const printData = [{
    ...billData
  }]
  lodop.preview(template, printData)
}

// 获取数据数量的方法
const getDataCount = () => {
  return detailTableModel.dataSource.length
}

defineExpose({
  reload,
  clearData,
  getDataCount
})
</script>
<template>
  <base-table :loading="detailTableModel.loading" :columns="detailTableModel.columns" :height="600"
              :dataSource="detailTableModel.dataSource" :pagination="detailTableModel.pagination" :custom-row="detailTableModel.customRow"
              :rowKey="(item: any) => item.wmReqid + '-' + item.lineNo">
    <template #btns>
      <a-space size="middle" class="m-l-10px">
        <a-space>
          <text>条目ID:</text>
          <a-input v-model:value="searchFormModel.S_EQ_t_wm_req_detail__Art_ID" style="width: 80px" @pressEnter="reload"/>
        </a-space>
        <a-space>
          <text>品名:</text>
          <a-input v-model:value="searchFormModel.S_LIKE_t_article__Art_Name" style="width: 150px" @pressEnter="reload"/>
        </a-space>
        <a-space>
          <text>患者:</text>
          <a-input v-model:value="searchFormModel.S_LIKE_t_visit__Patient_Name" style="width: 100px" @pressEnter="reload"/>
        </a-space>
        <a-checkbox v-model:checked="showShortFlag" @change="reload">只显示未完成</a-checkbox>
<!--        <a-button @click="handlePrintBill">-->
<!--          打印申领汇总单-->
<!--        </a-button>-->
        <a-button :loading="btnLoading" :disabled="detailTableModel.dataSource.length <= 0" type="primary" @click="onReserve()">库存锁定</a-button>
        <a-button :loading="btnLoading" :disabled="detailTableModel.dataSource.length <= 0" danger @click="onFinishedReq()">驳回申请</a-button>
      </a-space>
    </template>
    <template #bodyCell="{ column, record, index }">
      <template v-if="column?.dataIndex === 'index'">
        {{ index + 1 }}
      </template>
      <template v-if="column?.dataIndex === 'name'">
        {{ record.artName }} {{ record.artSpec }}<br/>{{ record.producer }}
      </template>
      <template v-if="column?.dataIndex === 'packCells'">
        {{ record.packCells }} {{ record.cellUnit }} / {{ record.packUnit }}
      </template>
      <!--      <template v-if="column?.dataIndex === 'totalPacks'">-->
      <!--        <span v-if="record.totalPacks">{{ record.totalPacks }}{{ record.packUnit }}</span><span v-if="record.totalCells">{{ record.totalCells }}{{ record.cellUnit }}</span>-->
      <!--      </template>-->
      <template v-if="column?.dataIndex === 'shortCells'">
<!--        <span v-if="record.shortCells">{{ record.shortCells }}{{ record.cellUnit }}</span>-->
        <span v-if="record.shortReqPacks">{{ record.shortReqPacks }}{{ record.packUnit }}</span>
        <span v-if="record.shortReqCells">{{ record.shortReqCells }}{{ record.cellUnit }}</span>
      </template>
      <template v-if="column?.dataIndex === 'cellsDelivered'">
        <span v-if="record.cellsDelivered > 0">{{ record.cellsDelivered }}{{ record.cellUnit }}</span>
      </template>
      <template v-if="column?.dataIndex === 'cellsReserved'">
        <span v-if="record.cellsReserved > 0">{{ record.cellsReserved }}{{ record.cellUnit }}</span>
      </template>
      <template v-if="column?.dataIndex === 'reservedFlag'">
        <span v-if="record.reservedFlag === 0">未锁定</span>
        <span v-if="record.reservedFlag === 1">完全锁定</span>
        <span v-if="record.reservedFlag === 2">部分锁定</span>
        <span v-if="record.reservedFlag === 3">完全缺货</span>
      </template>
    </template>
  </base-table>
</template>
<style lang="less" scoped>
.bg-fff {
  background-color: #fff;
  // padding: 8px 0 0 8px;

  :deep(.ant-tabs-nav) {
    margin-bottom: 0;
  }
  :deep(.ant-tabs-nav-list) {
    margin-left: 10px;
  }
}
</style>
