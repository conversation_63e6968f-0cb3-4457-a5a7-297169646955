{"name": "antdv-pro", "type": "module", "version": "1.1.0", "packageManager": "pnpm@8.10.0", "author": "aibayanyu <<EMAIL>>", "repository": "antdv-pro/antdv-pro", "engines": {"node": ">=18.15.0"}, "scripts": {"dev": "vite", "dev_oy": "vite --mode dev_oy", "build": "vue-tsc --noEmit --skipLibCheck && vite build --mode production", "build:pre": "vite build --mode production", "build:preview": "vite build --mode production_preview", "clear:vercel": "rm -rf ./vercel.json", "build:vercel": "run-s clear:vercel build:nitro", "build:nitro": "mist build nitro", "start:nirto": "node .output/server/index.mjs", "preview": "mist preview", "lint": "eslint src --fix", "typecheck": "vue-tsc --noEmit", "bump:patch": "changelogen --bump --output CHANGELOG.md --release", "bump:minor": "changelogen --bump --output CHANGELOG.md --release --minor", "bump:major": "changelogen --bump --output CHANGELOG.md --release --major", "prepare": "husky", "dir-tree": "esno ./scripts/dir-tree", "gen:uno": "esno ./scripts/gen-unocss", "toJS": "esno scripts/to-js"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@antv/g2plot": "^2.4.32", "@antv/l7": "^2.22.3", "@ctrl/tinycolor": "^4.1.0", "@idmy/antd": "0.0.119", "@idmy/core": "^1.0.141", "@mh-base/core": "^1.0.24", "@mh-hip/art-cat": "^1.0.0", "@mh-hip/art-sub-type": "^1.0.0", "@mh-hip/util": "^1.0.12", "@mh-inpatient-hsd/selector": "^1.0.7", "@mh-wm/batch-adjust": "^1.0.4", "@mh-wm/bills-track-code": "^1.0.3", "@mh-wm/count": "^1.0.6", "@mh-wm/pharmacy": "^1.0.1", "@mh-wm/recipe-track-code": "^1.0.11", "@mh-wm/scm-cust": "^1.0.8", "@mh-wm/util": "^1.0.14", "@mh-wm/whole-box": "^1.0.7", "@surely-vue/table": "^4.3.17", "@types/crypto-js": "^4.2.2", "@v-c/utils": "^0.0.26", "@vueuse/core": "^10.11.1", "ant-design-vue": "^4.2.6", "axios": "^1.7.9", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "lodash-es": "^4.17.21", "med-base-page": "1.1.82", "med-base-ui": "1.3.86", "mitt": "^3.0.1", "pinia": "^2.3.0", "vue": "^3.5.13", "vue-i18n": "^9.14.2", "vue-router": "^4.5.0", "vue3-cookies": "^1.0.6"}, "devDependencies": {"@antfu/eslint-config": "^2.27.3", "@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.3", "@mistjs/cli": "0.0.1-beta.7", "@mistjs/vite-plugin-preload": "^0.0.1", "@types/fs-extra": "^11.0.4", "@types/lodash": "^4.17.14", "@types/lodash-es": "^4.17.12", "@types/node": "^20.17.14", "@types/treeify": "^1.0.3", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/test-utils": "^2.4.6", "antdv-component-resolver": "^1.0.7", "antdv-style": "0.0.1-beta.2", "changelogen": "^0.5.7", "cross-env": "^7.0.3", "directory-tree": "^3.5.2", "esbuild": "^0.20.2", "eslint": "^8.57.1", "esno": "^0.17.0", "execa": "^8.0.1", "fs-extra": "^11.3.0", "husky": "^9.1.7", "jsdom": "^22.1.0", "less": "^4.2.1", "lint-staged": "^14.0.1", "lodash": "^4.17.21", "nitropack": "^2.10.4", "npm-run-all": "^4.1.5", "picocolors": "^1.1.1", "treeify": "^1.1.0", "ts-node": "^10.9.2", "typescript": "^5.7.3", "unocss": "^0.57.7", "unocss-preset-chinese": "^0.3.3", "unocss-preset-ease": "^0.0.3", "unplugin-auto-import": "^0.16.7", "unplugin-config": "^0.1.5", "unplugin-vue-components": "^0.26.0", "vite": "^5.4.11", "vite-plugin-compression2": "^0.11.0", "vitest": "^0.34.6", "vue-tsc": "^1.8.27"}, "lint-staged": {"**/*.{vue,ts,js,jsx,tsx}": "eslint src --fix"}}