<!--病区绑定追溯码组件示例-->
<template>
  <div class="section-track-code-example">
    <a-card title="病区绑定追溯码组件" class="mb-16px">
      <div class="mb-16px">
        <a-typography-title :level="4">病区绑定追溯码组件</a-typography-title>
        <a-typography-paragraph>
          病区绑定追溯码组件用于处理病区药品追溯码的绑定和分配，支持病区自行扫码绑定和药房预绑定追溯码分配两种模式。
          组件采用Modal弹窗设计，1500px宽度，支持大量药品和患者数据的处理。
        </a-typography-paragraph>

        <!-- 参数配置表单 -->
        <a-form
          ref="formRef"
          :model="formState"
          layout="vertical"
          class="parameter-form"
        >
          <a-form-item label="病区ID" name="sectionId" :rules="[{ required: true, message: '请输入病区ID' }]">
            <a-input-number
              v-model:value="formState.sectionId"
              :min="1"
              :max="999"
              placeholder="请输入病区ID"
              style="width: 200px;"
            />
            <div class="form-item-description">设置要查询的病区ID，默认为38</div>
          </a-form-item>

          <a-form-item label="Modal宽度" name="modalWidth">
            <a-input-number
              v-model:value="formState.modalWidth"
              :min="800"
              :max="2000"
              placeholder="Modal宽度"
              style="width: 200px;"
            />
            <div class="form-item-description">设置Modal弹窗的宽度，默认为1500px</div>
          </a-form-item>

          <a-form-item label="全局一键分配" name="autoAllocateAll">
            <a-switch
              v-model:checked="formState.autoAllocateAll"
              checked-children="开启"
              un-checked-children="关闭"
            />
            <div class="form-item-description">开启后在顶部显示"一键分配药房已扫码"按钮，可批量分配所有药房预绑定的追溯码</div>
          </a-form-item>

          <a-form-item>
            <a-space>
              <a-button type="primary" @click="openSectionTrackCode">打开病区绑定追溯码</a-button>
              <a-button @click="resetForm">重置</a-button>
              <a-button type="dashed" @click="useDefaultValues">使用默认值</a-button>
            </a-space>
          </a-form-item>
        </a-form>

        <a-divider />

        <a-typography-title :level="4">JSON格式参数</a-typography-title>
        <pre class="json-display">{
  "sectionId": {{ formState.sectionId }},
  "modalWidth": {{ formState.modalWidth }},
  "autoAllocateAll": {{ formState.autoAllocateAll }}
}</pre>

        <a-divider />

        <a-typography-title :level="4">功能特性</a-typography-title>
        <div class="features-grid">
          <a-card size="small" title="Modal弹窗设计">
            <p>1500px宽度，上下20px间距，最大化利用页面空间</p>
          </a-card>
          <a-card size="small" title="智能患者选择">
            <p>整包类单选，拆零类多选，自动计算需求量</p>
          </a-card>
          <a-card size="small" title="紧凑界面设计">
            <p>左侧药品卡片（30%），右侧患者网格（70%），适应大量数据</p>
          </a-card>
          <a-card size="small" title="床号排序">
            <p>患者按床号升序排列，优先满足前面床号的患者</p>
          </a-card>
          <a-card size="small" title="日期切换保护">
            <p>切换日期前检查未完成绑定，防止数据丢失</p>
          </a-card>
          <a-card size="small" title="拆零上报管理">
            <p>支持设置/取消拆零上报，无追溯码管理</p>
          </a-card>
        </div>

        <a-divider />

        <a-typography-title :level="4">代码示例</a-typography-title>
        <CodeDemoVue :usage="basicUsage" :importCode="importCode" :packageJson="packageJsonCode" />

        <a-divider />

        <a-typography-title :level="4">打包与发布</a-typography-title>
        <div class="code-example">
          <pre>
// 在项目根目录下执行以下命令打包并发布组件
pnpm publish:component InpatientHsd/SectionTrackCode

// 该命令会执行以下操作：
// 1. 编译组件源码
// 2. 生成类型声明文件
// 3. 打包CSS样式
// 4. 生成组件包到dist目录
// 5. 更新package.json中的版本号
// 6. 将组件发布到内部npm仓库
          </pre>
        </div>
      </div>
    </a-card>

    <!-- 病区绑定追溯码组件 -->
    <SectionTrackCode
      ref="sectionTrackCodeRef"
      :sectionId="formState.sectionId"
      :modalWidth="formState.modalWidth"
      :autoAllocateAll="formState.autoAllocateAll"
      @success="handleSuccess"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import SectionTrackCode from '@mh-inpatient-hsd/section-track-code'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { basicUsage, importCode, packageJsonCode } from './code/SectionTrackCodeCode'

// 表单引用
const formRef = ref()
const sectionTrackCodeRef = ref()

// 表单状态
const formState = reactive({
  sectionId: 38,
  modalWidth: 1500,
  autoAllocateAll: false
})

// 打开病区绑定追溯码组件
const openSectionTrackCode = () => {
  formRef.value.validate().then(() => {
    sectionTrackCodeRef.value.open()
  }).catch(error => {
    console.log('表单验证失败:', error)
    message.error('请填写必要的参数')
  })
}

// 重置表单
const resetForm = () => {
  formRef.value.resetFields()
}

// 使用默认值
const useDefaultValues = () => {
  formState.sectionId = 38
  formState.modalWidth = 1500
  formState.autoAllocateAll = false
}

// 成功回调
const handleSuccess = (data: any) => {
  message.success('追溯码绑定成功')
  console.log('绑定结果:', data)
}

// 取消回调
const handleCancel = () => {
  message.info('取消操作')
}
</script>

<style scoped>
.section-track-code-example {
  padding: 20px;
}

.parameter-form {
  max-width: 800px;
}

.json-display {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  font-family: monospace;
  overflow-x: auto;
}

.code-example {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  overflow: auto;
}

.code-example pre {
  margin: 0;
  font-family: monospace;
}

.form-item-description {
  font-size: 12px;
  color: #888;
  margin-top: 4px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin: 16px 0;
}

.features-grid .ant-card {
  height: 100%;
}

.features-grid .ant-card-body {
  padding: 12px;
}

.features-grid p {
  margin: 0;
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}
</style>
