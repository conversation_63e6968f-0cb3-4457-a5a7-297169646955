<!--药房-库房业务-药房发药-病区发药-按患者发药-->
<script setup lang="ts">
import { nextTick } from 'vue'
import {
  getSectionMegerReservedWbSeqidApi
} from "~/api/clinics_wm/wmreq.ts";
import WmBillArtTable from "./bill-art-table.vue";
import ReqArtLockedTable from './req-art-locked-table.vue'
import ReqArtUnlockTable from './req-art-unlock-table.vue'
const emit = defineEmits(['close', 'refresh'])

const reqArtUnlockTableRef = ref()
const reqArtLockedTableRef = ref()
const wmBillDetailTableRef = ref()

interface FormState {
  deptCode?: string;
  sectionId?: number;
  sectionName?: string | null;
  visitId?: number;
  patientName?: string | null;
  finishedFlag?: number;
}

const fomrState = ref<FormState>({})
const activeKey = ref('1')
const btnLoading = ref(false)
const isViewRef = ref(false)
const reservedWbSeqids = ref(null)

// 各个表格的数据数量
const waitDeliverCount = ref(0)
const unlockCount = ref(0)
const lockedCount = ref(0)

// 计算属性处理 null 值
const patientNameValue = computed(() => fomrState.value.patientName || '')
const sectionNameValue = computed(() => fomrState.value.sectionName || '')

const init = async (deptCode: string, sectionId: number | null, sectionName: string | null, visitId: number | null, patientName: string | null, finishedFlag: number) => {
  if (!sectionId || !visitId) {
    fomrState.value = { deptCode, finishedFlag }
    reservedWbSeqids.value = null
    reqArtUnlockTableRef.value?.clearData?.()
    reqArtLockedTableRef.value?.clearData?.()
    wmBillDetailTableRef.value?.clearData?.()

    // 重置数据数量
    waitDeliverCount.value = 0
    unlockCount.value = 0
    lockedCount.value = 0

    activeKey.value = '2'
    return
  }

  fomrState.value = {
    deptCode,
    sectionId,
    sectionName,
    visitId,
    patientName,
    finishedFlag
  }
  isViewRef.value = finishedFlag === 1
  await reload()
}

const reload = async () => {
  btnLoading.value = true
  await loadReservedInfo()
  await reqArtUnlockTableRef.value?.reload()
  await reqArtLockedTableRef.value?.reload()
  await wmBillDetailTableRef.value?.reload()

  // 更新各个表格的数据数量
  updateTableCounts()

  btnLoading.value = false
}

const loadReservedInfo = async () => {
  const { data } = await getSectionMegerReservedWbSeqidApi({
    deptCode: fomrState.value.deptCode,
    sectionId: fomrState.value.sectionId,
    visitId: fomrState.value.visitId
  })
  reservedWbSeqids.value = data

  // 如果有数据，默认选中第一行并触发查询明细信息
  if (data && data.length > 0) {
    // 这里可以添加选中第一行的逻辑
    console.log('有锁定数据，默认选中第一行:', data[0])

    // 触发数据数量更新
    nextTick(() => {
      updateTableCounts()
    })
  }
}

// 更新各个表格的数据数量
const updateTableCounts = () => {
  // 获取待出库明细数量
  waitDeliverCount.value = wmBillDetailTableRef.value?.getDataCount?.() || 0

  // 获取未锁定明细数量
  unlockCount.value = reqArtUnlockTableRef.value?.getDataCount?.() || 0

  // 获取已锁定明细数量
  lockedCount.value = reqArtLockedTableRef.value?.getDataCount?.() || 0

  // 如果待出库明细没有数据，且已锁定明细有数据，则跳转到已锁定明细tab
  if (waitDeliverCount.value === 0 && lockedCount.value > 0) {
    activeKey.value = '3'
  }
}



const lockedWaitDeliver = async () => {
  activeKey.value = '2'
  console.log('lockedWaitDeliver', reservedWbSeqids.value)
  await loadReservedInfo()
  console.log('lockedWaitDeliver loadReservedInfo', reservedWbSeqids.value)
  wmBillDetailTableRef.value?.reload(true)

  // 更新数据数量
  updateTableCounts()
}

defineExpose({
  init
})
</script>

<template>
  <a-tabs v-model:activeKey="activeKey">
    <a-tab-pane key="2" v-if="!isViewRef">
      <template #tab>
        <span v-if="waitDeliverCount > 0">待出库明细({{ waitDeliverCount }})</span>
        <span v-else>待出库明细</span>
      </template>
      <wm-bill-art-table
          ref="wmBillDetailTableRef"
          :wbSeqids="reservedWbSeqids || []"
          :deptCode="fomrState.deptCode"
          :sectionId="fomrState.sectionId"
          @refresh="$emit('refresh')"
          @dataLoaded="updateTableCounts" />
    </a-tab-pane>
    <a-tab-pane key="1">
      <template #tab>
        <span v-if="unlockCount > 0">未锁定明细({{ unlockCount }})</span>
        <span v-else>未锁定明细</span>
      </template>
      <req-art-unlock-table
        ref="reqArtUnlockTableRef"
        :deptCode="fomrState.deptCode"
        :sectionId="fomrState.sectionId"
        :visitId="fomrState.visitId"
        @ok="reload"
        @locked="lockedWaitDeliver"
        @refresh="$emit('refresh')"
        @dataLoaded="updateTableCounts" />
    </a-tab-pane>
    <a-tab-pane key="3">
      <template #tab>
        <span v-if="lockedCount > 0">已锁定明细({{ lockedCount }})</span>
        <span v-else>已锁定明细</span>
      </template>
      <req-art-locked-table
        ref="reqArtLockedTableRef"
        :deptCode="fomrState.deptCode"
        :sectionId="fomrState.sectionId"
        :visitId="fomrState.visitId"
        :finishedFlag="fomrState.finishedFlag"
        @dataLoaded="updateTableCounts" />
    </a-tab-pane>
    <template #rightExtra>
      <a-space style="padding-right: 20px">
        <a-form-item label="患者姓名" name="patientName">
          <a-input v-model:value="patientNameValue" :disabled="true"/>
        </a-form-item>
        <a-form-item label="病区名称" name="sectionName">
          <a-input v-model:value="sectionNameValue" :disabled="true"/>
        </a-form-item>
      </a-space>
    </template>
  </a-tabs>
</template>

<style lang="less" scoped>
.bg-fff {
  background-color: #fff;
  :deep(.ant-tabs-nav) {
    margin-bottom: 0;
  }
  :deep(.ant-tabs-nav-list) {
    margin-left: 10px;
  }
}
</style> 