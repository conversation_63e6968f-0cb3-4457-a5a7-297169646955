<script setup lang="ts">
import dayjs from "dayjs";
import type {PaginationProps} from 'ant-design-vue'
import {
  sectionMegerByPatientPageApi
} from '~/api/clinics_wm/wmreq.ts'
import OeReqForm from "./form.vue"

const props = defineProps({
  deptCode: { type: String }
})

interface TableModel {
  loading: boolean;
  columns: Array<{
    title: string;
    dataIndex: string;
    width: number;
    align: 'left' | 'right' | 'center';
  }>;
  dataSource: any[];
  selectedRowKeys: string[];
  pagination: PaginationProps;
  loadDataSource: () => Promise<void>;
}

const tableModel = reactive<TableModel>({
  loading: false,
  columns: [{
    title: '患者',
    dataIndex: 'patientName',
    width: 70,
    align: 'center'
  }, {
    title: '性别',
    dataIndex: 'genderName',
    width: 40,
    align: 'center'
  }, {
    title: '年龄',
    dataIndex: 'ageOfYears',
    width: 50,
    align: 'right'
  }, {
    title: '患者ID',
    dataIndex: 'patientId',
    width: 65,
    align: 'right'
  }],
  dataSource: [],
  selectedRowKeys: [],
  pagination: reactive<PaginationProps>({
    pageSize: 10,
    current: 1,
    total: 0,
    showQuickJumper: true,
    showTotal: (total: number) => `共：${total} 条`,
    onChange(current: number, pageSize: number) {
      tableModel.pagination.pageSize = pageSize
      tableModel.pagination.current = current
      tableModel.loadDataSource()
    },
  }),
  loadDataSource: async () => {
    tableModel.loading = true
    try {
      const {data} = await sectionMegerByPatientPageApi({
        ...searchFormModel,
        deptCode: props.deptCode,
        pageNum: tableModel.pagination.current,
        pageSize: tableModel.pagination.pageSize,
      })
      tableModel.dataSource = data.list
      tableModel.pagination.total = data.total ?? 0

      // 刷新事件后默认选中第一行数据
      if (data.list && data.list.length > 0) {
        const firstRecord = data.list[0]
        tableModel.selectedRowKeys = [firstRecord.visitId]

        // 调用 oeReqFormRef 的 init 方法，传递第一行数据
        if (oeReqFormRef.value) {
          oeReqFormRef.value.init(
            props.deptCode,
            firstRecord.sectionId,
            firstRecord.sectionName,
            firstRecord.visitId,
            firstRecord.patientName,
            searchFormModel.finishedFlag
          )
        }
      } else {
        // 如果没有数据，清空选中状态并初始化空状态
        tableModel.selectedRowKeys = []
        if (oeReqFormRef.value) {
          oeReqFormRef.value.init(props.deptCode, null, null, null, null, searchFormModel.finishedFlag)
        }
      }
    } catch (err) {
      console.error('加载数据失败:', err)
    } finally {
      tableModel.loading = false
    }
  }
})

const searchFormModel = reactive<{
  dateRange: dayjs.Dayjs[];
  finishedFlag: number;
}>({
  dateRange: [dayjs().add(-6, 'd'), dayjs()],
  finishedFlag: 0
})

const onSearchWmBill = async () => {
  tableModel.pagination.current = 1
  await tableModel.loadDataSource()
}

const oeReqFormRef = ref()

const customRow = (record: any) => {
  return {
    onClick: () => {
      // 更新选中状态
      tableModel.selectedRowKeys = [record.visitId]

      // 调用 oeReqFormRef 的 init 方法
      oeReqFormRef.value?.init(
        props.deptCode,
        record.sectionId,
        record.sectionName,
        record.visitId,
        record.patientName,
        searchFormModel.finishedFlag
      )
    }
  }
}

onMounted(async () => {
  await onSearchWmBill()
})

watch(() => props.deptCode, async () => {
  await onSearchWmBill()
})
</script>

<template>
  <a-row>
    <a-col :span="5">
      <base-table
        :loading="tableModel.loading"
        :columns="tableModel.columns"
        :dataSource="tableModel.dataSource"
        :rowKey="(item: any) => item.visitId"
        :custom-row="customRow"
        :pagination="tableModel.pagination"
        :row-selection="{
          selectedRowKeys: tableModel.selectedRowKeys,
          type: 'radio',
          onChange: (selectedRowKeys: any[], selectedRows: any[]) => {
            tableModel.selectedRowKeys = selectedRowKeys
            if (selectedRows.length > 0) {
              const record = selectedRows[0]
              oeReqFormRef?.init(
                props.deptCode,
                record.sectionId,
                record.sectionName,
                record.visitId,
                record.patientName,
                searchFormModel.finishedFlag
              )
            }
          }
        }">
        <template #btns>
          <a-space>
            <a-button type="primary" :loading="tableModel.loading" @click="onSearchWmBill">
              刷新
            </a-button>
          </a-space>
        </template>
        <template #bodyCell="{ column, record, index }">
          <template v-if="column?.dataIndex === 'ageOfYears'">
            <span v-if="record.ageOfYears > 6">{{ record.ageOfYears }} 岁</span>
            <span v-else>{{ record.ageOfYears }} 岁 {{ record.ageOfDays }} 天</span>
          </template>
        </template>
      </base-table>
    </a-col>
    <a-col :span="19">
      <oe-req-form ref="oeReqFormRef" width="1200px" @refresh="onSearchWmBill"/>
    </a-col>
  </a-row>
</template>

<style lang="less" scoped>
.bg-fff {
  background-color: #fff;
  :deep(.ant-tabs-nav) {
    margin-bottom: 0;
  }
  :deep(.ant-tabs-nav-list) {
    margin-left: 10px;
  }
}
</style> 