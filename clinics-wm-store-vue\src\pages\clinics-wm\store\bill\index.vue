<!--药库-库房业务-药库单据管理-->
<script setup lang="ts">
import dayjs from "dayjs";
import type { PaginationProps } from "ant-design-vue";
import {
  cancelApi,
  detailListApi,
  detailPageApi,
  downloadExcelByIdApi,
  pageApi,
  wmBillReturnApi,
  batchCheckApi,
} from "~/api/clinics_wm/wmbill";
import WmBillCheck from "../../comp/wm-bill-check.vue";
import { listApi as findAllDeptLsApi } from "~/api/clinics_wm/deptcustmap";
import {
  PrintTemplateType,
  getPrintTemplateFullApi,
} from "~/api/hip/printtemplate";
import StockChangeForm from "../../comp/stock-change-form.vue";
import PurchaseBillForm from "../../comp/purchase-bill-form.vue";
import lodop from "~/components/lodop/index";
import { pageByOrgApi } from "~/api/hip/article.ts";
import { findAllWmBillTypeApi } from "~/api/clinics_wm/wmbilltype.ts";
import { findAllBsnTypeApi } from "~/api/clinics_wm/businessType.ts";
import help from "~/utils/help.ts";
import { Modal } from "ant-design-vue";
import { StockReqCat } from "@mh-hip/art-cat";
import { ArtSubTypeDict } from "@mh-hip/art-sub-type";
import { WmDeptArtSelect } from "@mh-inpatient-hsd/selector";
import { ScmCustSelect } from "@mh-wm/scm-cust";
import filters from "~/utils/filters.ts";


const artSelectRef = ref<InstanceType<typeof WmDeptArtSelect>>();
const message = useMessage();
const statusLs = reactive([
  {
    title: "已取消",
    value: -1,
  },
  {
    title: "未提交",
    value: 0,
  },
  {
    title: "待接收",
    value: 1,
  },
  {
    title: "已接收",
    value: 2,
  },
  {
    title: "调配中",
    value: 3,
  },
  {
    title: "待领取",
    value: 4,
  },
  {
    title: "已完成",
    value: 5,
  },
]);
// 加载数据
interface TableModel {
  loading?: Boolean;
  columns: any[];
  dataSource: any[];
  selectedRowKeys: any[];
}

const tableModel = reactive<TableModel>({
  loading: false,
  columns: [
    {
      title: "序号",
      dataIndex: "index",
      width: 50,
      align: "right",
    },
    {
      title: "票据流水",
      dataIndex: "wbSeqid",
      width: 80,
      align: "right",
    },
    {
      title: "相关流水号",
      dataIndex: "relativeWbseqid",
      width: 90,
      align: "right",
    },
    {
      title: "单据类型",
      dataIndex: "wmbillTypeName",
      width: 75,
      align: "center",
    },
    {
      title: "业务类型",
      dataIndex: "bsnTypeName",
      width: 75,
      align: "center",
    },
    {
      title: "状态",
      dataIndex: "statusName",
      width: 60,
      align: "center",
    },
    {
      title: "成本金额",
      dataIndex: "cost",
      width: 100,
      align: "right",
    },
    {
      title: "金额",
      dataIndex: "amount",
      width: 100,
      align: "right",
    },
    {
      title: "往来单位",
      dataIndex: "custName",
      width: 250,
      ellipsis: true,
      align: "left",
    },
    {
      title: "审核人",
      dataIndex: "validatorUname",
      width: 90,
      align: "center",
    },
    {
      title: "审核时间",
      dataIndex: "timeValidated",
      width: 100,
      align: "center",
    },
    {
      title: "制单人",
      dataIndex: "creatorUname",
      width: 90,
      align: "center",
    },
    {
      title: "制单时间",
      dataIndex: "timeCreated",
      width: 100,
      align: "center",
    },
    {
      title: "摘要",
      dataIndex: "bsnAbstract",
      width: 350,
      align: "left",
    },
    {
      title: "说明备注",
      dataIndex: "reqNotes",
      width: 350,
      align: "left",
    },
    {
      title: "操作",
      dataIndex: "action",
      width: 180,
      fixed: "right",
      align: "center",
    },
  ],
  dataSource: [],
  sumAmount: {},
  loadDataSource: async () => {
    tableModel.loading = true;
    try {
      const { data } = await pageApi({
        ...searchFormModel,
        S_GE_t_wm_bill__Time_Created:
          searchFormModel.dateRange[0].format("YYYY-MM-DD"),
        S_LT_t_wm_bill__Time_Created: searchFormModel.dateRange[1]
          .add(1, "d")
          .format("YYYY-MM-DD"),
        sidx: "t_wm_bill.wb_seqid",
        order: "desc",
        pageNum: tableModel.pagination.current,
        pageSize: tableModel.pagination.pageSize,
      });
      tableModel.dataSource = data.list;
      tableModel.pagination.total = data.total ?? 0;
      tableModel.sumAmount = data.extra;
      tableModel.loading = false;
      detailTableModel.dataSource = [];
      tableModel.selectedRowKeys = [];
    } catch (err) {
      console.log(err);
      tableModel.loading = false;
    }
  },
  selectedRowKeys: [],
  pagination: reactive<PaginationProps>({
    pageSize: 10,
    current: 1,
    total: 0,
    showQuickJumper: true,
    showTotal: (total) => `共：${tableModel.pagination.total} 条`,
    onChange(current, pageSize) {
      tableModel.pagination.pageSize = pageSize;
      tableModel.pagination.current = current;
      tableModel.loadDataSource();
    },
  }),
});

const detailTableModel = reactive<TableModel>({
  loading: false,
  columns: [
    {
      title: "行号",
      dataIndex: "lineNo",
      width: 60,
      align: "right",
    },
    {
      title: "条目ID",
      dataIndex: "artId",
      width: 90,
      resizable: true,
    },
    {
      title: "医保编码",
      dataIndex: "miCode",
      width: 150,
      resizable: true,
    },
    {
      title: "品名|规格|厂家",
      dataIndex: "name",
      width: 300,
      resizable: true,
    },
    {
      title: "包装规格",
      dataIndex: "packCells",
      width: 100,
      resizable: true,
      align: "right",
    },
    {
      title: "原产地",
      dataIndex: "originPlace",
      width: 120,
      resizable: true,
      align: "right",
      // }, {
      //   title: '拆零单位',
      //   dataIndex: 'cellUnit',
      //   width: 75,
      //   resizable: true,
      //   align: 'center'
      // }, {
      //   title: '拆零系数',
      //   dataIndex: 'packCells',
      //   width: 75,
      //   resizable: true,
      //   align: 'right'
    },
    {
      title: "生产日期",
      dataIndex: "dateManufactured",
      width: 80,
      resizable: true,
      align: "center",
    },
    {
      title: "生产批号",
      dataIndex: "batchNo",
      width: 100,
      resizable: true,
    },
    {
      title: "有效期至",
      dataIndex: "expiry",
      width: 100,
      resizable: true,
      align: "center",
    },
    {
      title: "整包数量",
      dataIndex: "totalPacks",
      width: 100,
      resizable: true,
      align: "right",
    },
    {
      title: "整包单价",
      dataIndex: "packPrice",
      width: 100,
      resizable: true,
      align: "right",
    },
    {
      title: "拆零数量",
      dataIndex: "totalCells",
      width: 100,
      resizable: true,
      align: "right",
    },
    {
      title: "拆零单价",
      dataIndex: "cellPrice",
      width: 100,
      resizable: true,
      align: "right",
    },
    {
      title: "金额",
      dataIndex: "amount",
      width: 100,
      resizable: true,
      align: "right",
    },
    {
      title: "备注",
      dataIndex: "notes",
      resizable: true,
    },
  ],
  dataSource: [],
  loadDataSource: async () => {
    detailTableModel.loading = true;
    try {
      const { data } = await detailPageApi({
        wbSeqid: currentWbSeqid.value,
        pageNum: detailTableModel.pagination.current,
        pageSize: detailTableModel.pagination.pageSize,
      });
      detailTableModel.dataSource = data.list;
      detailTableModel.pagination.total = data.total ?? 0;
      detailTableModel.loading = false;
    } catch (err) {
      console.log(err);
      detailTableModel.loading = false;
    }
  },
  pagination: reactive<PaginationProps>({
    pageSize: 10,
    current: 1,
    total: 0,
    showQuickJumper: true,
    showTotal: (total) => `共：${detailTableModel.pagination.total} 条`,
    onChange(current, pageSize) {
      detailTableModel.pagination.pageSize = pageSize;
      detailTableModel.pagination.current = current;
      detailTableModel.loadDataSource();
    },
  }),
});
const searchFormModel = reactive<any>({});
const onSearchWmBill = async () => {
  tableModel.pagination.current = 1;
  tableModel.loadDataSource();
};

const onReset = () => {
  searchFormModel.dateRange = [dayjs().add(-6, "d"), dayjs()];
  searchFormModel.pendingFlag = 1;
  searchFormModel.S_EQ_t_wm_bill__Bsn_Type_ID = undefined;
  searchFormModel.S_EQ_t_wm_bill__WMBill_Type_ID = undefined;
  searchFormModel.S_EQ_t_wm_bill__Status = undefined;
  searchFormModel.custId = undefined;
  searchFormModel.Section_Name = undefined;
  searchFormModel.S_EQ_t_wm_bill__WB_SeqID = undefined;
  searchFormModel.artId = undefined;
  searchFormModel.artName = undefined;
  searchFormModel.artCatTypeId = undefined;
  searchFormModel.artSubTypeId = undefined;
  searchFormModel.bsnAbstract = undefined;
  artSelectRef.value?.init();
  onSearchWmBill();
};

// 往来单位选择处理函数
const handleCustChange = (value: any) => {
  console.log("选中的往来单位:", value);

  if (typeof value === "object" && value !== null) {
    searchFormModel.custId = value.id || value.custId;
  } else if (value) {
    searchFormModel.custId = value;
  } else {
    searchFormModel.custId = undefined;
  }

  onSearchWmBill();
};

const bsnTypeIdLs = ref<any[]>([]);
const getBsnTypeIdLs = async () => {
  const { data } = await findAllBsnTypeApi({});
  bsnTypeIdLs.value = data;
};
const wmBillTypeLs = ref<any[]>([]);
const getWmBillTypeLs = async () => {
  const { data } = await findAllWmBillTypeApi({});
  wmBillTypeLs.value = data;
};

const billTypeLs = computed(() => {
  if (searchFormModel.S_EQ_t_wm_bill__Bsn_Type_ID) {
    return wmBillTypeLs.value.filter(
      (item) => item.bsnTypeId === searchFormModel.S_EQ_t_wm_bill__Bsn_Type_ID
    );
  } else {
    return wmBillTypeLs.value;
  }
});

const onChangeBsnTypeId = () => {
  if (searchFormModel.S_EQ_t_wm_bill__WMBill_Type_ID) {
    const exists = billTypeLs.value.find(
      (item: any) =>
        item.wmbillTypeId === searchFormModel.S_EQ_t_wm_bill__WMBill_Type_ID
    );
    if (!exists) {
      searchFormModel.S_EQ_t_wm_bill__WMBill_Type_ID = undefined;
    }
  }
  onSearchWmBill();
};

const deptLs = ref<any[]>([]);

const getDeptLs = async () => {
  const { data } = await findAllDeptLsApi({
    isChecker: 1,
  });
  deptLs.value = data;
  if (data.length > 0) {
    searchFormModel.deptCode = data[0].deptCode;
  }
};
onMounted(async () => {
  await getDeptLs();
  await getWmBillTypeLs();
  await getBsnTypeIdLs();
  searchFormModel.dateRange = [dayjs().add(-6, "d"), dayjs()];
  searchFormModel.pendingFlag = 1;
  await onSearchWmBill();
});
onActivated(async () => {
  if (searchFormModel.deptCode) {
    await onSearchWmBill();
  }
});
// onActivated(async () => {
//   await onSearchWmBill()
// })
const rangePresets = ref([
  { label: "当天", value: [dayjs(), dayjs()] },
  { label: "最近3日", value: [dayjs().add(-2, "d"), dayjs()] },
  { label: "最近7日", value: [dayjs().add(-6, "d"), dayjs()] },
  { label: "最近14日", value: [dayjs().add(-13, "d"), dayjs()] },
  { label: "最近30日", value: [dayjs().add(-29, "d"), dayjs()] },
]);
const customRow = (record) => {
  return {
    onClick: () => {
      selectRow(record);
    },
  };
};
const rowSelection = computed(() => {
  return {
    type: "checkbox",
    selectedRowKeys: tableModel.selectedRowKeys,
    onChange: (selectedRowKeys) => {
      tableModel.selectedRowKeys = selectedRowKeys;
    },
  };
});
const currentWbSeqid = ref(null);
const selectRow = (record) => {
  const selectedRowKeys = [...tableModel.selectedRowKeys];
  if (selectedRowKeys.indexOf(record.wbSeqid) >= 0) {
    selectedRowKeys.splice(selectedRowKeys.indexOf(record.wbSeqid), 1);
    // currentWbSeqid.value = null
  } else {
    // selectedRowKeys.splice(0, 1)
    selectedRowKeys.push(record.wbSeqid);
  }
  tableModel.selectedRowKeys = selectedRowKeys;
  currentWbSeqid.value = record.wbSeqid;
  loadDetail();
};
const loadDetail = async () => {
  detailTableModel.pagination.current = 1;
  detailTableModel.loadDataSource();
};
// const wmBillFormRef = ref()
const wmBillCheckRef = ref();
// const wmBillFormVisible = ref(false)
const wmBillCheckVisible = ref(false);

const stockChangeFormVisible = ref(false);
const stockChangeFormRef = ref();
const purchaseBillFormVisible = ref(false);
const purchaseBillFormRef = ref();
const editBill = (record) => {
  if (record.bsnType === 1 || record.bsnType === 5) {
    purchaseBillFormVisible.value = true;
    purchaseBillFormRef.value?.init(record.deptCode, record.wbSeqid);
  } else if (record.bsnType === 3) {
    // 损溢单
    stockChangeFormVisible.value = true;
    stockChangeFormRef.value?.init(record.deptCode, record.wbSeqid);
  } else {
    message.error("该单据类型不允许编辑");
  }
};
const onCancelBill = async (record) => {
  console.log(record);
  await cancelApi({
    wbSeqid: record.wbSeqid,
  });
  message.success("作废成功");
  await onSearchWmBill();
};
const checkBill = (record) => {
  console.log(record);
  wmBillCheckVisible.value = true;
  wmBillCheckRef.value?.init(record?.wbSeqid);
};
const onReturnWmBill = async (record) => {
  await wmBillReturnApi({
    wbSeqid: record.wbSeqid,
  });
  message.success("冲红提交成功，请进行后续操作");
  await onSearchWmBill();
};
const onCloseForm = () => {
  // wmBillFormVisible.value = false
  wmBillCheckVisible.value = false;
  stockChangeFormVisible.value = false;
  purchaseBillFormVisible.value = false;
  onSearchWmBill();
};

/**
 * 转换打印数据中的单位信息
 * 根据业务类型和单据类型调整调出单位和收货单位
 * @param record - 原始单据数据
 * @returns - 转换后的打印数据
 */
const transformPrintData = (record: any) => {
  // 使用JSON序列化进行深拷贝，避免修改原对象
  const printRecord = JSON.parse(JSON.stringify(record));

  if (printRecord.bsnType === 4) {
    if (printRecord.sectionName) {
      // 药房与病区的往来
      if (printRecord.wmbillTypeId === 32) {
        // 病区退回药房：调出单位是病区，收货单位是药房
        printRecord.deptName = printRecord.sectionName; // 调出单位（病区）
        printRecord.applyDeptname = record.deptName; // 收货单位（药房）
      } else {
        // 其它类型：调出单位是药房，收货单位是病区
        printRecord.deptName = record.deptName; // 调出单位（药房）
        printRecord.applyDeptname = printRecord.sectionName; // 收货单位（病区）
      }
    } else if (printRecord.wmbillTypeId === 31) {
      // 调拨出库：调出单位是record.deptName，收货单位是record.transferDeptName
      printRecord.deptName = record.deptName;
      printRecord.applyDeptname = record.transferDeptName;
    } else if (printRecord.wmbillTypeId === 32) {
      // 调拨退回：调出单位是record.transferDeptName，收货单位是record.deptName
      printRecord.deptName = record.transferDeptName;
      printRecord.applyDeptname = record.deptName;
    } else if (printRecord.wmbillTypeId === 41) {
      // 调拨入库：调出单位是record.transferDeptName，收货单位是record.deptName
      printRecord.deptName = record.transferDeptName;
      printRecord.applyDeptname = record.deptName;
    } else if (printRecord.wmbillTypeId === 42) {
      // 调拨退出：调出单位是record.deptName，收货单位是record.transferDeptName
      printRecord.deptName = record.deptName;
      printRecord.applyDeptname = record.transferDeptName;
    }
  }

  return printRecord;
}

const handlePrintBill = async (record: any) => {
  const { data } = await detailListApi({
    wbSeqid: record.wbSeqid,
  });

  // 转换打印数据
  const printRecord = transformPrintData(record);

  let template;
  if (printRecord.bsnType === 1 || printRecord.bsnType === 5) {
    // 采购单
    template = await getPrintTemplateFullApi(PrintTemplateType.wm_pu_bill);
    //采购单需要增加原产地打印（草药产地）
    if (data && data.length > 0) {
      data.forEach((item) => {
        if (item.originPlace && item.originPlace.trim()) {
          item.producer = `${item.producer ? item.producer + "  " : ""}${
            item.originPlace
          }`;
        }
        if (printRecord.wmbillTypeId === 12) {
          item.total = -Math.abs(item.total || 0);
          item.amount = -Math.abs(item.amount || 0);
          item.saleAmount = -Math.abs(item.saleAmount || 0);
        }
      });
    }
  } else if (printRecord.bsnType === 4) {
    //调拨
    template = await getPrintTemplateFullApi(PrintTemplateType.wm_trans_bill);
  } else {
    //损溢、销售
    template = await getPrintTemplateFullApi(PrintTemplateType.wm_bill);
  }
  if (template.tempItems) {
    template.tempItems = JSON.parse(template.tempItems);
  }
  const printData = [
    {
      ...printRecord,
      details: data,
    },
  ];
  lodop.preview(template, printData);
};

// 药品选择
function handleArtSelect(art: any) {
  if (art) {
    searchFormModel.artName = art.artName;
    searchFormModel.artId = art.artId;
  } else {
    artSelectRef.value?.init();
  }
}

const onDownloadExcel = async (record: any) => {
  const res: any = await downloadExcelByIdApi({
    wbSeqid: record.wbSeqid,
  });
  help.expExcel(res, record.wbSeqid + "单据明细");
};
const onBatchCheck = (isPass: number) => {
  console.log(isPass);
  let content =
    "是否批量审核通过当前已经选择的" +
    tableModel.selectedRowKeys.length +
    "条记录";
  if (isPass === 0) {
    content =
      "是否批量驳回当前已经选择的" +
      tableModel.selectedRowKeys.length +
      "条记录";
  }
  Modal.confirm({
    title: "操作提示",
    content: content,
    cancelText: "取消",
    okText: "确定",
    onOk() {
      batchCheck(isPass);
    },
  });
};

const batchCheck = async (isPass: any) => {
  tableModel.loading = true;
  await batchCheckApi({
    wbdSeqids: tableModel.selectedRowKeys,
    isPass: isPass,
  })
    .then(() => {
      message.success("操作成功");
      tableModel.loading = false;
      onSearchWmBill();
    })
    .catch(() => {
      tableModel.loading = false;
    });
};

// 合集
const tableSummary = computed(() => {
  return [
    {
      index: 1,
      colSpan: 2,
      label: "合计：",
      style: {},
    },
    {
      index: 7,
      colSpan: 1,
      label: tableModel.sumAmount.Cost,
      style: {
        textAlign: "right",
      },
    },
    {
      index: 8,
      colSpan: 1,
      label: tableModel.sumAmount.Amount,
      style: {
        textAlign: "right",
      },
    },
  ];
});
</script>

<template>
  <page-container>
    <div class="bg-fff box-shadow p-16px h-full">
      <a-form
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 14 }"
        :model="searchFormModel"
        w-full
      >
        <a-row>
          <a-col flex="200px">
            <a-form-item label="仓库" name="deptCode">
              <a-select
                v-model:value="searchFormModel.deptCode"
                placeholder="请选择仓库"
                @change="onSearchWmBill"
              >
                <a-select-option
                  v-for="item in deptLs"
                  :key="item.deptCode"
                  :value="item.deptCode"
                  >{{ item.deptName }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col flex="350px">
            <a-form-item
              label="制单日期"
              name="startDate"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 17 }"
            >
              <a-range-picker
                :presets="rangePresets"
                v-model:value="searchFormModel.dateRange"
                @change="onSearchWmBill"
                :allow-clear="false"
              />
            </a-form-item>
          </a-col>
          <a-col flex="200px">
            <a-form-item label="业务类型" name="S_EQ_t_wm_bill__Bsn_Type_ID">
              <a-select
                v-model:value="searchFormModel.S_EQ_t_wm_bill__Bsn_Type_ID"
                placeholder="请选择业务类型"
                @change="onChangeBsnTypeId"
                allow-clear
              >
                <a-select-option
                  v-for="item in bsnTypeIdLs"
                  :key="item.bsnTypeId"
                  :value="item.bsnTypeId"
                  >{{ item.bsnTypeName }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col flex="200px">
            <a-form-item label="单据类型" name="S_EQ_t_wm_bill__WMBill_Type_ID">
              <a-select
                v-model:value="searchFormModel.S_EQ_t_wm_bill__WMBill_Type_ID"
                placeholder="请选择单据类型"
                @change="onSearchWmBill"
                allow-clear
              >
                <a-select-option
                  v-for="item in billTypeLs"
                  :key="item.wmbillTypeId"
                  :value="item.wmbillTypeId"
                  >{{ item.wmbillTypeName }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col flex="300px">
            <a-form-item
              label="进行中"
              name="pendingFlag"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 19 }"
            >
              <a-radio-group
                v-model:value="searchFormModel.pendingFlag"
                @change="onSearchWmBill"
              >
                <a-radio :value="1">未完成</a-radio>
                <a-radio :value="0">全部</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col flex="200px">
            <a-form-item label="状态" name="S_EQ_t_wm_bill__Status">
              <a-select
                v-model:value="searchFormModel.S_EQ_t_wm_bill__Status"
                placeholder="请选择状态"
                @change="onSearchWmBill"
                allow-clear
              >
                <a-select-option
                  v-for="item in statusLs"
                  :key="item.value"
                  :value="item.value"
                  >{{ item.title }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col flex="350px">
            <a-form-item
              label="往来单位"
              name="custId"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 17 }"
            >
              <ScmCustSelect
                v-model="searchFormModel.custId"
                style="width: 250px"
                @change="handleCustChange"
                placeholder="请选择往来单位"
                :enablePagination="false"
                :orgPartner="true"
              />
            </a-form-item>
          </a-col>
          <a-col flex="200px">
            <a-form-item label="病区名称" name="Section_Name">
              <a-input
                v-model:value="searchFormModel.Section_Name"
                placeholder="请输入病区名称"
                @pressEnter="onSearchWmBill"
              />
            </a-form-item>
          </a-col>
          <a-col flex="200px">
            <a-form-item label="票据流水" name="S_EQ_t_wm_bill__WB_SeqID">
              <a-input
                v-model:value="searchFormModel.S_EQ_t_wm_bill__WB_SeqID"
                placeholder="请输入票据流水"
                @pressEnter="onSearchWmBill"
              />
            </a-form-item>
          </a-col>
          <a-col flex="300px">
            <a-form-item
              label="说明"
              name="bsnAbstract"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 19 }"
            >
              <a-input
                v-model:value="searchFormModel.bsnAbstract"
                placeholder="说明"
                @pressEnter="onSearchWmBill"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row m-b-2>
          <a-col flex="200px">
            <a-form-item label="条目ID" name="artId">
              <a-input
                v-model:value="searchFormModel.artId"
                placeholder="请输入条目ID"
                @pressEnter="onSearchWmBill"
              />
            </a-form-item>
          </a-col>
          <a-col flex="350px">
            <a-form-item
              label="品种查询"
              name="artName"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 17 }"
            >
              <wm-dept-art-select
                :deptCode="searchFormModel.deptCode"
                ref="artSelectRef"
                @selected="handleArtSelect"
              />
            </a-form-item>
          </a-col>
          <a-col flex="200px">
            <a-form-item label="条目分类">
              <StockReqCat
                v-model="searchFormModel.artCatTypeId"
                type="Select"
              />
            </a-form-item>
          </a-col>
          <a-col flex="200px">
            <a-form-item label="条目亚类">
              <ArtSubTypeDict
                v-model="searchFormModel.artSubTypeId"
                type="Select"
              />
            </a-form-item>
          </a-col>
          <a-col flex="100px">
            <a-space>
              <a-button
                type="primary"
                :loading="tableModel.loading"
                @click="onSearchWmBill"
                m-l-2
              >
                查询
              </a-button>
              <a-button @click="onReset" m-l-2> 重置 </a-button>
            </a-space>
          </a-col>
          <a-col flex="auto"> </a-col>
          <a-col flex="250px">
            <a-button
              type="primary"
              @click="onBatchCheck(1)"
              :loading="tableModel.loading"
              :disabled="tableModel.selectedRowKeys.length === 0"
              m-r-2
              >批量审核通过</a-button
            >
            <a-button
              type="primary"
              @click="onBatchCheck(0)"
              :loading="tableModel.loading"
              :disabled="tableModel.selectedRowKeys.length === 0"
              danger
              >批量审核驳回</a-button
            >
          </a-col>
        </a-row>
      </a-form>
      <base-table
        :row-selection="rowSelection"
        :loading="tableModel.loading"
        :columns="tableModel.columns"
        :dataSource="tableModel.dataSource"
        rowKey="wbSeqid"
        :scroll="{ x: 1000, y: 300 }"
        :custom-row="customRow"
        :pagination="tableModel.pagination"
        :summary="tableSummary"
      >
        <template #bodyCell="{ column, record, index, text }">
          <template v-if="column?.dataIndex === 'name'">
            <a-button type="link">{{ record.name }}</a-button>
          </template>
          <template v-if="column?.dataIndex === 'index'">
            {{ index + 1 }}
          </template>
          <template v-if="column?.dataIndex === 'custName'">
            {{ record.sectionName || record.custName }}
          </template>
          <template v-if="column?.dataIndex === 'statusName'">
            <span v-if="record.status === -1" c-error>{{ text }}</span>
            <span v-else>{{ text }}</span>
          </template>
          <template
            v-if="['timeCreated', 'timeValidated'].includes(column.dataIndex)"
          >
            {{ filters.dateFormatMDHM(text) }}
          </template>
          <!--          <template v-if="column?.dataIndex === 'bsnType'">-->
          <!--            <span v-if="record.bsnType === 1">采购入库</span>-->
          <!--            <span v-if="record.bsnType === 2">销售出库</span>-->
          <!--            <span v-if="record.bsnType === 3">库存损溢</span>-->
          <!--            <span v-if="record.bsnType === 4">库存调拨</span>-->
          <!--          </template>-->
          <!--          <template v-if="column?.dataIndex === 'status'">-->
          <!--            <span v-if="record.status === -1">已取消</span>-->
          <!--            <span v-if="record.status === 0">未提交</span>-->
          <!--            <span v-if="record.status === 1">待接收</span>-->
          <!--            <span v-if="record.status === 2">已接收</span>-->
          <!--            <span v-if="record.status === 3">调配中</span>-->
          <!--            <span v-if="record.status === 4">待领取</span>-->
          <!--            <span v-if="record.status === 5">已完成</span>-->
          <!--          </template>-->
          <template v-if="column?.dataIndex === 'action'">
            <a-space>
              <a
                v-if="
                  record.status === 0 &&
                  (record.bsnType === 1 ||
                    record.bsnType === 3 ||
                    record.bsnType === 5)
                "
                @click="editBill(record)"
                >编辑</a
              >
              <!--              <a-popconfirm title="确定作废吗？" ok-text="确定" cancel-text="取消" @confirm="onCancelBill(record)"-->
              <!--                            v-if="record.status === 0">-->
              <!--                <template #icon>-->
              <!--                  <question-circle-outlined style="color: red"/>-->
              <!--                </template>-->
              <!--                <a c-error>-->
              <!--                  作废-->
              <!--                </a>-->
              <!--              </a-popconfirm>-->
              <a v-if="record.status === 1" @click="checkBill(record)">审核</a>
              <a @click="handlePrintBill(record)">打印票据</a>
              <!--              1、采购入库冲红在库存管理已加功能 2、销售出库还能冲红没确定逻辑暂时不启用-->
              <!--              <a-popconfirm title="此操作将产生冲红单据，如果有费用会产生退费单，确定冲红吗？" ok-text="确定" cancel-text="取消" @confirm="onReturnWmBill(record)"-->
              <!--                            v-if="(record.bsnType === 1 || record.bsnType === 2) && (record.wmbillTypeId === 11 || record.wmbillTypeId === 21) && record.status === 5 && !record.relativeWbseqid">-->
              <!--                <template #icon>-->
              <!--                  <question-circle-outlined style="color: red"/>-->
              <!--                </template>-->
              <!--                <a c-error>-->
              <!--                  冲红-->
              <!--                </a>-->
              <!--              </a-popconfirm>-->
              <a @click="onDownloadExcel(record)">导出明细</a>
            </a-space>
          </template>
        </template>
      </base-table>
      <base-table
        :loading="detailTableModel.loading"
        :columns="detailTableModel.columns"
        :dataSource="detailTableModel.dataSource"
        rowKey="lineNo"
        :pagination="detailTableModel.pagination"
      >
        <template #btns> </template>
        <template #bodyCell="{ column, record, index }">
          <template v-if="column?.dataIndex === 'name'">
            {{ record.artName }} {{ record.artSpec }}<br />{{ record.producer }}
          </template>
          <template v-if="column?.dataIndex === 'packCells'">
            {{ record.packCells }} {{ record.cellUnit }} / {{ record.packUnit }}
          </template>
        </template>
      </base-table>
    </div>
    <wm-bill-check
      ref="wmBillCheckRef"
      v-model:visible="wmBillCheckVisible"
      width="1200px"
      @close="onCloseForm"
    />
    <stock-change-form
      ref="stockChangeFormRef"
      v-model:visible="stockChangeFormVisible"
      width="1200px"
      @close="onCloseForm"
    />
    <purchase-bill-form
      ref="purchaseBillFormRef"
      v-model:visible="purchaseBillFormVisible"
      width="1200px"
      @close="onCloseForm"
    />
  </page-container>
</template>

<style lang="less" scoped>
.bg-fff {
  background-color: #fff;
  // padding: 8px 0 0 8px;

  :deep(.ant-tabs-nav) {
    margin-bottom: 0;
  }

  :deep(.ant-tabs-nav-list) {
    margin-left: 10px;
  }
}
</style>
