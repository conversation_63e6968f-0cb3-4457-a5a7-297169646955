<!--药房-库房业务-药房发药-病区发药-按病区发药-->
<script setup lang="ts">
import { nextTick } from 'vue'

import {
  reqCancelApi,
  getSectionMegerReservedWbSeqidApi,
  stockReserveBySectionApi,
  releaseStockByWbSeqidApi, reqDeliverBySectionApi, finishedReqBySectionApi
} from "~/api/clinics_wm/wmreq.ts";
import WmBillArtTable from "./bill-art-table.vue";
// import {Modal} from "ant-design-vue";
// import {createVNode} from "vue";
// import {ExclamationCircleOutlined} from "@ant-design/icons-vue";
import ReqArtLockedTable from './req-art-locked-table.vue'
import ReqArtUnlockTable from './req-art-unlock-table.vue'

const message = useMessage()
const emit = defineEmits(['close', 'refresh'])
const modalVisible = defineModel('visible', {
  type: Boolean,
  default: false
})
const reqArtUnlockTableRef = ref()
const reqArtLockedTableRef = ref()
const wmBillDetailTableRef = ref()

interface FormState {
  deptCode?: string;
  sectionId?: number | null;
  sectionName?: string | null;
  finishedFlag?: number;
}

const fomrState = ref<FormState>({})
const activeKey = ref('1')
const btnLoading = ref(false)
const isViewRef = ref(false)
const reservedWbSeqids = ref(null)

// 各个表格的数据数量
const waitDeliverCount = ref(0)
const unlockCount = ref(0)
const lockedCount = ref(0)

// 用户手动切换tab的标志
const isUserManualSwitch = ref(false)
// 是否是初始加载
const isInitialLoad = ref(true)

// 计算属性处理 null 值
const sectionNameValue = computed(() => fomrState.value.sectionName || '')

const init = async (deptCode: string, sectionId: number | null, sectionName: string | null, finishedFlag: number) => {
  console.log(deptCode, sectionId)
  fomrState.value.deptCode = deptCode
  fomrState.value.sectionId = sectionId
  fomrState.value.sectionName = sectionName
  fomrState.value.finishedFlag = finishedFlag
  isViewRef.value = finishedFlag === 1

  // 如果sectionId为null，清空相关数据
  if (!sectionId) {
    // 清空表单数据
    fomrState.value = { deptCode, finishedFlag }
    // 清空锁定的单号
    reservedWbSeqids.value = null
    // 清空各个表格数据
    reqArtUnlockTableRef.value?.clearData?.()
    reqArtLockedTableRef.value?.clearData?.()
    wmBillDetailTableRef.value?.clearData?.()

    // 重置数据数量
    waitDeliverCount.value = 0
    unlockCount.value = 0
    lockedCount.value = 0

    // 重置默认选项卡到待出库明细
    setActiveKeyInternal('2')
    return
  }

  // 选中数据时，重置到待出库明细tab，优先查看待出库明细
  setActiveKeyInternal('2')

  // 重置标志，允许自动切换
  isUserManualSwitch.value = false
  isInitialLoad.value = true

  await reload()
}
const reload = async () => {
  btnLoading.value = true
  await loadReservedInfo()

  // 先查询待出库明细，再查询未锁定明细
  await wmBillDetailTableRef.value?.reload()
  await reqArtUnlockTableRef.value?.reload()
  await reqArtLockedTableRef.value?.reload()

  // 等待一个tick确保所有数据都已加载完成
  await nextTick()

  // 使用统一的数据加载完成处理方法
  onDataLoaded()

  btnLoading.value = false
}
// 获取锁定的单号
const loadReservedInfo = async () => {
  const { data } = await getSectionMegerReservedWbSeqidApi({
    deptCode: fomrState.value.deptCode,
    sectionId: fomrState.value.sectionId,
  })
  reservedWbSeqids.value = data

  // 如果有数据，默认选中第一行并触发查询明细信息
  if (data && data.length > 0) {
    console.log('有锁定数据，默认选中第一行:', data[0])
    // 这里可以添加选中第一行的逻辑
  }
}

// 更新各个表格的数据数量
const updateTableCounts = () => {
  // 获取待出库明细数量
  waitDeliverCount.value = wmBillDetailTableRef.value?.getDataCount?.() || 0

  // 获取未锁定明细数量
  unlockCount.value = reqArtUnlockTableRef.value?.getDataCount?.() || 0

  // 获取已锁定明细数量
  lockedCount.value = reqArtLockedTableRef.value?.getDataCount?.() || 0

  console.log('数据数量更新:', {
    waitDeliverCount: waitDeliverCount.value,
    unlockCount: unlockCount.value,
    lockedCount: lockedCount.value,
    currentActiveKey: activeKey.value
  })
}

// 处理数据加载完成后的自动切换逻辑
const handleAutoTabSwitch = () => {
  // 只在初始加载时或非用户手动切换时进行自动切换
  if (!isUserManualSwitch.value) {
    let targetTab = activeKey.value

    // 自动切换tab逻辑：优先显示有数据的tab
    // 1. 如果待出库明细有数据，显示待出库明细
    // 2. 如果待出库明细没有数据，但未锁定明细有数据，显示未锁定明细
    // 3. 如果前两者都没有数据，但已锁定明细有数据，显示已锁定明细
    // 4. 如果待出库明细与未锁定明细都没有数据时，默认选中待出库明细tab页
    if (waitDeliverCount.value > 0) {
      targetTab = '2'
    } else if (unlockCount.value > 0) {
      targetTab = '1'
    } else if (lockedCount.value > 0) {
      targetTab = '3'
    } else if (isInitialLoad.value) {
      // 只在初始加载时设置默认tab，避免覆盖用户的手动选择
      targetTab = '2'
    }

    if (targetTab !== activeKey.value) {
      setActiveKeyInternal(targetTab)
      console.log('自动切换到tab:', targetTab)
    }
  } else {
    console.log('用户手动切换，跳过自动切换逻辑')
  }

  // 初始加载完成后，设置标志
  if (isInitialLoad.value) {
    isInitialLoad.value = false
  }
}

// 统一的数据加载完成处理方法
const onDataLoaded = () => {
  // 延迟一点时间确保所有组件都已更新
  setTimeout(() => {
    updateTableCounts()
    handleAutoTabSwitch()
  }, 100)
}

// 监听activeKey变化，检测用户手动切换
let isInternalChange = false
watch(activeKey, (newVal, oldVal) => {
  if (!isInternalChange && !isInitialLoad.value) {
    // 用户手动切换了tab
    isUserManualSwitch.value = true
    console.log('检测到用户手动切换tab:', oldVal, '->', newVal)
  }
})

// 内部切换activeKey的方法
const setActiveKeyInternal = (key: string) => {
  isInternalChange = true
  activeKey.value = key
  nextTick(() => {
    isInternalChange = false
  })
}

// // 库存锁定
// const onReserve = async () => {
//   btnLoading.value = true
//   try {
//     const { data } = await stockReserveBySectionApi({
//       deptCode: fomrState.value.deptCode,
//       sectionId: fomrState.value.sectionId,
//     })
//     // reservedWbSeqid.value = data
//     await loadReservedInfo()
//     // btnLoading.value = false
//     message.success('操作成功')
//     await wmReqArtTableRef.value?.reload()
//     await wmBillDetailTableRef.value?.reload()
//   } catch (err) {
//     console.log(err)
//     message.error('库存锁定失败')
//   } finally {
//     btnLoading.value = false
//   }
//   // activeKey.value = '2'
//   // if (fomrState.value.reservedFlag === 1) {
//   //   activeKey.value = '2'
//   //   message.success('库存锁定成功')
//   // } else {
//   //   activeKey.value = '2'
//   //   message.warning('库存不能全部满足，请查看具体明细')
//   // }
// }
// // 驳回申请，取消发药
// const onCancel = async (record) => {
//   btnLoading.value = true
//   try {
//     await reqCancelApi({
//       wmReqid: fomrState.value.wmReqid
//     })
//     // btnLoading.value = false
//     message.success('操作成功')
//     emit('close')
//   } catch (err) {
//     console.log(err)
//     message.error('库存锁定失败')
//   } finally {
//     btnLoading.value = false
//   }
// }
// // 取消锁定
// const onReleaseStock = async (record) => {
//   btnLoading.value = true
//   try {
//     await releaseStockByWbSeqidApi({
//       wbSeqids: reservedWbSeqids.value
//     })
//     await loadReservedInfo()
//     // btnLoading.value = false
//     message.success('操作成功')
//     await reqArtUnlockTableRef.value?.reload()
//     await reqArtLockedTableRef.value?.reload()
//     await wmBillDetailTableRef.value?.clearData()
//     activeKey.value = '2'
//   } catch (err) {
//     console.log(err)
//     message.error('库存锁定失败')
//   } finally {
//     btnLoading.value = false
//   }
// }

const lockedWaitDeliver = async () => {
  setActiveKeyInternal('2')
  await loadReservedInfo()
  await wmBillDetailTableRef.value?.reload(true)

  // 等待一个tick确保数据加载完成
  await nextTick()

  // 使用统一的数据加载完成处理方法
  onDataLoaded()
}

// // 发药确认
// const onDeliver = async (record) => {
//   btnLoading.value = true
//   try {
//     await reqDeliverBySectionApi({
//       wbSeqids: reservedWbSeqids.value
//     })
//     // btnLoading.value = false
//     message.success('操作成功')
//     // reservedWbSeqid.value = null
//     await loadReservedInfo()
//     await reqArtUnlockTableRef.value?.reload()
//     await reqArtLockedTableRef.value?.reload()
//     await wmBillDetailTableRef.value?.clearData()
//   } catch (err) {
//     console.log(err)
//     message.error('库存锁定失败')
//   } finally {
//     btnLoading.value = false
//   }
// }
// const onFinishedReq = async (record) => {
//   Modal.confirm({
//     title: '驳回申请?',
//     icon: createVNode(ExclamationCircleOutlined),
//     content: createVNode('div', {}, '此操作会将当前病区所有的领用申请全部驳回，请确定不需要再发药了再操作!'),
//     onOk () {
//       finishedReq()
//     },
//     onCancel() {
//     },
//     class: 'test',
//   });
// }
const finishedReq = async (record) => {
  btnLoading.value = true
  try {
    await finishedReqBySectionApi({
      deptCode: fomrState.value.deptCode,
      sectionId: fomrState.value.sectionId,
    })
    message.success('操作成功')
  } catch (err) {
    console.log(err)
    message.error('结束请求失败')
  } finally {
    btnLoading.value = false
  }
}
const onClose = () => {
  emit('close')
}
defineExpose({
  init
})
</script>
<template>
<!--  <a-modal v-model:open="modalVisible" @cancel="onClose" :footer="null">-->
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="2" v-if="!isViewRef">
        <template #tab>
          <span v-if="waitDeliverCount > 0">待出库明细({{ waitDeliverCount }})</span>
          <span v-else>待出库明细</span>
        </template>
        <wm-bill-art-table
            ref="wmBillDetailTableRef"
            :wbSeqids="reservedWbSeqids || []"
            :deptCode="fomrState.deptCode"
            :sectionId="fomrState.sectionId || undefined"
            @refresh="$emit('refresh')"
            @dataLoaded="onDataLoaded" />
      </a-tab-pane>
      <a-tab-pane key="1">
        <template #tab>
          <span v-if="unlockCount > 0">未锁定明细({{ unlockCount }})</span>
          <span v-else>未锁定明细</span>
        </template>
        <req-art-unlock-table
            ref="reqArtUnlockTableRef"
            :deptCode="fomrState.deptCode"
            :sectionId="fomrState.sectionId || undefined"
            @ok="reload"
            @locked="lockedWaitDeliver"
            @refresh="$emit('refresh')"
            @dataLoaded="onDataLoaded" />
      </a-tab-pane>
      <a-tab-pane key="3">
        <template #tab>
          <span v-if="lockedCount > 0">已锁定明细({{ lockedCount }})</span>
          <span v-else>已锁定明细</span>
        </template>
        <req-art-locked-table
            ref="reqArtLockedTableRef"
            :deptCode="fomrState.deptCode"
            :sectionId="fomrState.sectionId || undefined"
            :finishedFlag="fomrState.finishedFlag"
            @refresh="$emit('refresh')"
            @dataLoaded="onDataLoaded" />
      </a-tab-pane>
      <template #rightExtra>
        <a-space style="padding-right: 20px">
          <a-form-item label="病区名称" name="sectionName">
            <a-input v-model:value="sectionNameValue" :disabled="true"/>
          </a-form-item>
        </a-space>
<!--        <a-space style="padding-right: 50px;" v-if="!isViewRef">-->
<!--          <a-button :loading="btnLoading" v-if="!reservedWbSeqids && fomrState.sectionId" type="primary" @click="onReserve()">库存锁定</a-button>-->
<!--          <a-button :loading="btnLoading" v-if="!reservedWbSeqids && fomrState.sectionId" danger @click="onFinishedReq()">驳回申请</a-button>-->
<!--          <a-button :loading="btnLoading" v-if="reservedWbSeqids" @click="onReleaseStock()" danger>取消锁定</a-button>-->
<!--          <a-button :loading="btnLoading" v-if="reservedWbSeqids" type="primary" @click="onDeliver()">发药确认</a-button>-->
<!--        </a-space>-->
      </template>
    </a-tabs>
<!--  </a-modal>-->
</template>
<style lang="less" scoped>
.bg-fff {
  background-color: #fff;
  // padding: 8px 0 0 8px;

  :deep(.ant-tabs-nav) {
    margin-bottom: 0;
  }
  :deep(.ant-tabs-nav-list) {
    margin-left: 10px;
  }
}
</style>
